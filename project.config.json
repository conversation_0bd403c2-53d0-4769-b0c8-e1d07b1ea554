{"miniprogramRoot": "dist/", "projectname": "recycle", "description": "电子卡回收", "appid": "wx91227ba038eb741f", "setting": {"urlCheck": true, "es6": false, "enhance": false, "compileHotReLoad": false, "postcss": false, "minified": false, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "srcMiniprogramRoot": "dist/", "condition": {}}