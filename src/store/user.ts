import type { UserResponse } from '../api/types/user'
import { userApi } from '../api/user'
import request from '../utils/request'
import type { Dispatch } from 'redux'

// Action Types
export const SET_USER_INFO = 'SET_USER_INFO'
export const CLEAR_USER_INFO = 'CLEAR_USER_INFO'
export const UPDATE_USER_INFO = 'UPDATE_USER_INFO'
export const FETCH_USER_INFO_REQUEST = 'FETCH_USER_INFO_REQUEST'
export const FETCH_USER_INFO_SUCCESS = 'FETCH_USER_INFO_SUCCESS'
export const FETCH_USER_INFO_FAILURE = 'FETCH_USER_INFO_FAILURE'

// Action 接口定义
export interface SetUserInfoAction {
  type: typeof SET_USER_INFO
  payload: UserResponse
}

export interface ClearUserInfoAction {
  type: typeof CLEAR_USER_INFO
}

export interface UpdateUserInfoAction {
  type: typeof UPDATE_USER_INFO
  payload: Partial<UserResponse>
}

export interface FetchUserInfoRequestAction {
  type: typeof FETCH_USER_INFO_REQUEST
}

export interface FetchUserInfoSuccessAction {
  type: typeof FETCH_USER_INFO_SUCCESS
  payload: UserResponse
}

export interface FetchUserInfoFailureAction {
  type: typeof FETCH_USER_INFO_FAILURE
  payload: string
}

export type UserActionTypes =
  | SetUserInfoAction
  | ClearUserInfoAction
  | UpdateUserInfoAction
  | FetchUserInfoRequestAction
  | FetchUserInfoSuccessAction
  | FetchUserInfoFailureAction

// Action Creators
export const setUserInfo = (userInfo: UserResponse): SetUserInfoAction => ({
  type: SET_USER_INFO,
  payload: userInfo
})

export const clearUserInfo = (): ClearUserInfoAction => ({
  type: CLEAR_USER_INFO
})

export const updateUserInfo = (userInfo: Partial<UserResponse>): UpdateUserInfoAction => ({
  type: UPDATE_USER_INFO,
  payload: userInfo
})

// 异步Action Creators
export const fetchUserInfoRequest = (): FetchUserInfoRequestAction => ({
  type: FETCH_USER_INFO_REQUEST
})

export const fetchUserInfoSuccess = (userInfo: UserResponse): FetchUserInfoSuccessAction => ({
  type: FETCH_USER_INFO_SUCCESS,
  payload: userInfo
})

export const fetchUserInfoFailure = (error: string): FetchUserInfoFailureAction => ({
  type: FETCH_USER_INFO_FAILURE,
  payload: error
})

/**
 * 获取当前用户信息并持久化到store
 * 这是一个异步action，使用redux-thunk中间件
 * 如果获取失败，会清空用户store并删除本地缓存中的token
 */
export const fetchCurrentUserInfo = () => {
  return async (dispatch: Dispatch<UserActionTypes>) => {
    try {
      // 开始请求
      dispatch(fetchUserInfoRequest())

      // 调用API获取用户信息
      const userInfo = await userApi.getCurrentUserInfo()

      // 请求成功，更新store
      dispatch(fetchUserInfoSuccess(userInfo))
      dispatch(setUserInfo(userInfo))

      return userInfo
    } catch (error) {
      // 请求失败，记录错误
      const errorMessage = error instanceof Error ? error.message : '获取用户信息失败'
      dispatch(fetchUserInfoFailure(errorMessage))

      // 清空用户store
      dispatch(clearUserInfo())

      // 删除本地缓存中的token
      request.clearAuth()

      // 重新抛出错误，让调用方可以处理
      throw error
    }
  }
}

// State 类型定义
export interface UserState {
  userInfo: UserResponse | null
  loading: boolean
  error: string | null
}

// 初始状态
const INITIAL_STATE: UserState = {
  userInfo: null,
  loading: false,
  error: null
}

// Reducer
export default function userReducer(state = INITIAL_STATE, action: UserActionTypes): UserState {
  switch (action.type) {
    case SET_USER_INFO:
      return {
        ...state,
        userInfo: action.payload,
        loading: false,
        error: null
      }

    case CLEAR_USER_INFO:
      return {
        ...state,
        userInfo: null,
        loading: false,
        error: null
      }

    case UPDATE_USER_INFO:
      return {
        ...state,
        userInfo: state.userInfo ? {
          ...state.userInfo,
          ...action.payload
        } : null,
        loading: false,
        error: null
      }

    case FETCH_USER_INFO_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      }

    case FETCH_USER_INFO_SUCCESS:
      return {
        ...state,
        userInfo: action.payload,
        loading: false,
        error: null
      }

    case FETCH_USER_INFO_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload
      }

    default:
      return state
  }
}
