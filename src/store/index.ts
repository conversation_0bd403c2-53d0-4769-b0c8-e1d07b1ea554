import { createStore, applyMiddleware, compose } from 'redux'
import { thunk } from 'redux-thunk'
import rootReducer from './reducers'

// Redux DevTools 扩展
const composeEnhancers =
  typeof window === 'object' && (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__
    ? (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({
        // 指定扩展的选项，如 name, actionsBlacklist, actionsCreators, serialize...
      })
    : compose

// 中间件配置
const middlewares = [thunk]

// 开发环境添加 logger
if (process.env.NODE_ENV === 'development') {
  middlewares.push(require('redux-logger').createLogger())
}

const enhancer = composeEnhancers(
  applyMiddleware(...middlewares)
  // 其他 store enhancers
)

export default function configStore() {
  const store = createStore(rootReducer, enhancer)
  return store
}

// 导出 RootState 类型
export type RootState = ReturnType<typeof rootReducer>
