import { View, Text, Input, Image } from '@tarojs/components'
import { useState, useEffect } from 'react'
import { navigateTo } from '@tarojs/taro'
import './index.scss'

interface SearchBarProps {
  placeholder?: string
  onSearch?: (value: string) => void
  showCustomerService?: boolean
  onCustomerServiceClick?: () => void
  /** 是否启用跳转到搜索页面，默认true */
  enableNavigation?: boolean
  /** 搜索框的值 */
  value?: string
  /** 是否需要padding，默认true */
  needPadding?: boolean
}

export default function SearchBar({
  placeholder = '请输入您想找，想出售的东西',
  onSearch,
  showCustomerService = true,
  onCustomerServiceClick,
  enableNavigation = true,
  value = '',
  needPadding = true
}: SearchBarProps) {
  const [searchValue, setSearchValue] = useState<string>(value)

  // 当外部传入的value变化时，更新内部状态
  useEffect(() => {
    setSearchValue(value)
  }, [value])

  const handleSearch = () => {
    const keyword = searchValue.trim()
    if (!keyword) return

    // 如果有自定义搜索回调，优先使用
    if (onSearch) {
      onSearch(keyword)
    } else if (enableNavigation) {
      // 默认跳转到搜索页面
      navigateTo({
        url: `/pages/search/index?keyword=${encodeURIComponent(keyword)}`
      })
    }
  }

  // 处理键盘确认事件
  const handleConfirm = () => {
    handleSearch()
  }

  const handleCustomerServiceClick = () => {
    if (onCustomerServiceClick) {
      onCustomerServiceClick()
    }
  }

  return (
    <View className={`search-bar ${needPadding ? 'with-padding' : 'no-padding'}`}>
      <View className='search-input-wrapper'>
        <Image
          className='search-icon'
          src={require('@/assets/images/search_icon.png')}
          mode='aspectFit'
        />
        <Input
          className='search-input'
          placeholder={placeholder}
          placeholderStyle='font-size: 22rpx; color: #999;'
          value={searchValue}
          onInput={(e) => setSearchValue(e.detail.value)}
          onConfirm={handleConfirm}
          confirmType='search'
        />
        <View className='search-btn' onClick={handleSearch}>
          <Text>搜索</Text>
        </View>
      </View>
      {showCustomerService && (
        <View className='customer-service' onClick={handleCustomerServiceClick}>
          <Image
            src={require('@/assets/images/index_kefu.png')}
            className='customer-service-icon'
            mode='aspectFit'
          />
        </View>
      )}
    </View>
  )
}
