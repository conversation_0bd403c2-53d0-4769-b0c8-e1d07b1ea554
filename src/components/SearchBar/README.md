# SearchBar 搜索栏组件

一个可复用的搜索栏组件，包含搜索输入框和可选的客服按钮。

## 功能特性

- 搜索输入框，支持自定义占位符文本
- 搜索按钮，点击时触发搜索回调
- 可选的客服按钮，支持自定义点击事件
- 响应式设计，适配不同屏幕尺寸
- 主题色支持，使用全局CSS变量

## Props

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| placeholder | string | '请输入您想找，想出售的东西' | 否 | 搜索框占位符文本 |
| onSearch | (value: string) => void | - | 否 | 搜索回调函数，参数为搜索内容 |
| showCustomerService | boolean | true | 否 | 是否显示客服按钮 |
| onCustomerServiceClick | () => void | - | 否 | 客服按钮点击回调 |

## 使用示例

### 基础用法

```tsx
import { SearchBar } from '@/components'

function MyPage() {
  const handleSearch = (value: string) => {
    console.log('搜索内容:', value)
    // 处理搜索逻辑
  }

  return (
    <View>
      <SearchBar onSearch={handleSearch} />
    </View>
  )
}
```

### 自定义配置

```tsx
import { SearchBar } from '@/components'

function MyPage() {
  const handleSearch = (value: string) => {
    // 搜索逻辑
  }

  const handleCustomerService = () => {
    // 客服逻辑
  }

  return (
    <View>
      <SearchBar
        placeholder="请输入商品名称"
        onSearch={handleSearch}
        onCustomerServiceClick={handleCustomerService}
        showCustomerService={true}
      />
    </View>
  )
}
```

### 隐藏客服按钮

```tsx
import { SearchBar } from '@/components'

function MyPage() {
  return (
    <View>
      <SearchBar
        placeholder="搜索..."
        showCustomerService={false}
      />
    </View>
  )
}
```

## 样式定制

组件使用了全局CSS变量，可以通过修改 `app.scss` 中的主题色变量来定制样式：

- `--primary-color`: 主题色
- `--primary-light`: 主题浅色

## 注意事项

1. 组件依赖 `@/assets/images/search_icon.png` 和 `@/assets/images/index_kefu.png` 图片资源
2. 需要在项目中引入全局样式变量（app.scss）
3. 搜索框的值由组件内部管理，外部无法直接控制
