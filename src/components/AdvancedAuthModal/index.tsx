import { View, Text } from '@tarojs/components'
import './index.scss'

interface AdvancedAuthModalProps {
  visible: boolean
  onClose: () => void
}

export default function AdvancedAuthModal({ visible, onClose }: AdvancedAuthModalProps) {
  if (!visible) return null

  return (
    <View className='advanced-auth-modal'>
      <View className='modal-overlay' onClick={onClose}></View>
      <View className='modal-content'>
        {/* 标题栏 */}
        <View className='modal-header'>
          <Text className='modal-title'>高级实名认证</Text>
          <View className='close-btn' onClick={onClose}>
            <Text className='close-icon'>×</Text>
          </View>
        </View>

        {/* 说明文字 */}
        <View className='modal-body'>
          <Text className='instruction-text'>
            请截屏保存二维码用微信扫一扫选择相册二维码，按照提示操作可完成高级实名认证
          </Text>

          {/* 二维码区域 */}
          <View className='qrcode-container'>
            <View className='qrcode-border'>
              <View className='qrcode-placeholder'>
                <Text className='placeholder-text'>二维码占位</Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  )
}
