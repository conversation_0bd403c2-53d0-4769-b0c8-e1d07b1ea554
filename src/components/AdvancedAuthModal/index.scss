.advanced-auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 600px;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 标题栏 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px 32px 24px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.modal-title {
  font-size: 32px;
  font-weight: normal;
  color: #333;
}

.close-btn {
  position: absolute;
  right: 32px;
  top: 50%;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-icon {
  font-size: 36px;
  color: #999;
  line-height: 1;
}

/* 弹窗内容 */
.modal-body {
  padding: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
}

.instruction-text {
  font-size: 24px;
  color: #1890ff;
  line-height: 1.6;
  text-align: center;
}

/* 二维码区域 */
.qrcode-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.qrcode-border {
  width: 400px;
  height: 400px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.qrcode-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.placeholder-text {
  font-size: 28px;
  color: #999;
}

/* 底部说明 */
.bottom-notice {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.notice-text {
  font-size: 24px;
  color: #999;
  text-align: center;
  line-height: 1.5;
}
