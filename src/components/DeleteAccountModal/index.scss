.delete-account-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 遮罩层 */
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

/* 弹窗内容 */
.modal-content {
  position: relative;
  width: 600px;
  background: white;
  border-radius: 24px;
  padding: 0;
  margin: 0 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 弹窗头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 48px 48px 32px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  cursor: pointer;
  transition: background 0.3s ease;

  &:active {
    background: #e6e6e6;
  }
}

.close-icon {
  font-size: 32px;
  color: #666;
  line-height: 1;
}

/* 密码输入区域 */
.password-section {
  padding: 32px 48px 40px;
}

.password-input-wrapper {
  position: relative;
}

.password-input {
  width: 100%;
  height: 88px;
  background: #f8f8f8;
  border: 1px solid #e6e6e6;
  border-radius: 12px;
  padding: 0 32px;
  padding-right: 120px; /* 为忘记密码文字留出空间 */
  font-size: 28px;
  color: #333;
  box-sizing: border-box;

  &::placeholder {
    color: #999;
  }

  &:focus {
    border-color: #1890ff;
    background: white;
  }
}

.forgot-password {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24px;
  color: #ff4d4f;
  cursor: pointer;
  padding: 8px;
  z-index: 10;
  pointer-events: auto; /* 确保可以点击 */

  &:active {
    opacity: 0.7;
  }
}

/* 弹窗底部 */
.modal-footer {
  padding: 0 48px 48px;
}

.confirm-btn {
  width: 100%;
  height: 88px;
  background: #1890ff;
  border: none;
  border-radius: 44px;
  font-size: 28px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);
    background: #096dd9;
  }

  &::after {
    border: none;
  }
}
