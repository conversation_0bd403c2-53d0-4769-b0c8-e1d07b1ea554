import { View, Text, Input, Button } from '@tarojs/components'
import { useState } from 'react'
import Taro from '@tarojs/taro'
import './index.scss'

interface DeleteAccountModalProps {
  /** 是否显示弹窗 */
  visible: boolean
  /** 账户类型 */
  accountType: 'alipay' | 'bank'
  /** 关闭弹窗 */
  onClose: () => void
  /** 确认删除 */
  onConfirm: (password: string) => void
}

export default function DeleteAccountModal({
  visible,
  accountType,
  onClose,
  onConfirm
}: DeleteAccountModalProps) {
  const [tradePassword, setTradePassword] = useState('')

  // 处理确认删除
  const handleConfirm = () => {
    if (!tradePassword.trim()) {
      Taro.showToast({
        title: '请输入交易密码',
        icon: 'none'
      })
      return
    }

    onConfirm(tradePassword.trim())
    setTradePassword('')
  }

  // 处理关闭
  const handleClose = () => {
    setTradePassword('')
    onClose()
  }

  // 忘记密码
  const handleForgotPassword = (e: any) => {
    console.log('点击了忘记密码')

    // 阻止事件冒泡，防止触发输入框的点击事件
    e.stopPropagation()
    e.preventDefault()

    // 先关闭弹窗
    handleClose()

    // 延迟一下再跳转，确保弹窗完全关闭
    setTimeout(() => {
      console.log('开始跳转到交易密码页面')
      Taro.navigateTo({
        url: '/pages/trade-password/index'
      }).then(() => {
        console.log('跳转到交易密码页面成功')
      }).catch((error) => {
        console.error('跳转失败:', error)
        Taro.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      })
    }, 100)
  }

  if (!visible) return null

  return (
    <View className='delete-account-modal'>
      <View className='modal-mask' onClick={handleClose}></View>
      <View className='modal-content'>
        {/* 标题和关闭按钮 */}
        <View className='modal-header'>
          <Text className='modal-title'>
            删除{accountType === 'alipay' ? '支付宝账号' : '银行卡'}
          </Text>
          <View className='close-btn' onClick={handleClose}>
            <Text className='close-icon'>×</Text>
          </View>
        </View>

        {/* 密码输入区域 */}
        <View className='password-section'>
          <View className='password-input-wrapper'>
            <Input
              className='password-input'
              type='password'
              password
              placeholder='请输入交易密码'
              value={tradePassword}
              onInput={(e) => setTradePassword(e.detail.value)}
              maxlength={20}
            />
            <View
              className='forgot-password'
              onClick={handleForgotPassword}
              catchMove
            >
              <Text>忘记密码？</Text>
            </View>
          </View>
        </View>

        {/* 确认按钮 */}
        <View className='modal-footer'>
          <Button
            className='confirm-btn'
            onClick={handleConfirm}
          >
            确认删除
          </Button>
        </View>
      </View>
    </View>
  )
}
