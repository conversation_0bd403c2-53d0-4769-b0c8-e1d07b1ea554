.bank-selector {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

/* 遮罩层 */
.selector-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

/* 选择器内容 */
.selector-content {
  position: relative;
  width: 100%;
  height: 80vh;
  background: white;
  border-radius: 24px 24px 0 0;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 头部 */
.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32px 32px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.header-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  cursor: pointer;
  transition: background 0.3s ease;

  &:active {
    background: #e6e6e6;
  }
}

.close-icon {
  font-size: 32px;
  color: #666;
  line-height: 1;
}

/* 搜索区域 */
.search-section {
  padding: 24px 32px;
  border-bottom: 1px solid #f0f0f0;
}

.search-input {
  width: 100%;
  height: 72px;
  background: #f8f8f8;
  border: 1px solid #e6e6e6;
  border-radius: 12px;
  padding: 0 24px;
  font-size: 28px;
  color: #333;
  box-sizing: border-box;

  &::placeholder {
    color: #999;
  }

  &:focus {
    border-color: #1890ff;
    background: white;
  }
}

/* 银行列表 */
.bank-list {
  flex: 1;
  padding: 0 32px 32px;
  max-height: 50vh;
}

.bank-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f8f8f8;
  }

  &.selected {
    background: #f0f9ff;
  }
}

.bank-name {
  font-size: 28px;
  color: #333;
  flex: 1;
}

.selected-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1890ff;
  border-radius: 50%;
}

.check-icon {
  font-size: 20px;
  color: white;
  font-weight: 600;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
}

.empty-text {
  font-size: 28px;
  color: #999;
}
