import { View, Text, Input, ScrollView } from '@tarojs/components'
import { useState, useEffect } from 'react'
import './index.scss'

interface BankSelectorProps {
  /** 是否显示选择器 */
  visible: boolean
  /** 银行列表 */
  bankList: string[]
  /** 当前选中的银行 */
  selectedBank: string
  /** 关闭选择器 */
  onClose: () => void
  /** 选择银行 */
  onSelect: (bank: string) => void
}

export default function BankSelector({
  visible,
  bankList,
  selectedBank,
  onClose,
  onSelect
}: BankSelectorProps) {
  const [searchText, setSearchText] = useState('')
  const [filteredBanks, setFilteredBanks] = useState<string[]>([])

  useEffect(() => {
    if (visible) {
      setSearchText('')
      setFilteredBanks(bankList)
    }
  }, [visible, bankList])

  // 搜索银行
  const handleSearch = (text: string) => {
    setSearchText(text)
    if (!text.trim()) {
      setFilteredBanks(bankList)
    } else {
      const filtered = bankList.filter(bank =>
        bank.toLowerCase().includes(text.toLowerCase())
      )
      setFilteredBanks(filtered)
    }
  }

  // 选择银行
  const handleSelectBank = (bank: string) => {
    onSelect(bank)
    onClose()
  }

  // 处理关闭
  const handleClose = () => {
    setSearchText('')
    onClose()
  }

  if (!visible) return null

  return (
    <View className='bank-selector'>
      <View className='selector-mask' onClick={handleClose}></View>
      <View className='selector-content'>
        {/* 头部 */}
        <View className='selector-header'>
          <Text className='header-title'>开户银行</Text>
          <View className='close-btn' onClick={handleClose}>
            <Text className='close-icon'>×</Text>
          </View>
        </View>

        {/* 搜索框 */}
        <View className='search-section'>
          <Input
            className='search-input'
            placeholder='搜索银行'
            value={searchText}
            onInput={(e) => handleSearch(e.detail.value)}
          />
        </View>

        {/* 银行列表 */}
        <ScrollView
          className='bank-list'
          scrollY
          enhanced
          showScrollbar={false}
        >
          {filteredBanks.map((bank, index) => (
            <View
              key={index}
              className={`bank-item ${selectedBank === bank ? 'selected' : ''}`}
              onClick={() => handleSelectBank(bank)}
            >
              <Text className='bank-name'>{bank}</Text>
              {selectedBank === bank && (
                <View className='selected-icon'>
                  <Text className='check-icon'>✓</Text>
                </View>
              )}
            </View>
          ))}

          {filteredBanks.length === 0 && (
            <View className='empty-state'>
              <Text className='empty-text'>未找到相关银行</Text>
            </View>
          )}
        </ScrollView>
      </View>
    </View>
  )
}
