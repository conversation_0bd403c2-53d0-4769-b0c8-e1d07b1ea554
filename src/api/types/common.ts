// 通用接口相关的数据类型定义

// 首页推荐响应
export interface HomeRecommendationResponse {
  /** 推荐ID */
  id: number
  /** 文案标题 */
  title: string
  /** 描述 */
  description: string
  /** 图片链接 */
  image_url: string
  /** 跳转链接 */
  jump_url: string
  /** 排序 */
  sort: number
}

// 系统配置响应
export interface SystemConfigResponse {
  /** 热门搜索关键词列表 */
  hot_search: string[]
  /** 公告 */
  notice: string
  /** ICP备案号 */
  icp_number: string
  /** 首页推荐列表 */
  home_recommendations: HomeRecommendationResponse[]
  /** 首页主图URL */
  hero_image_url: string
}

// 帮助信息响应
export interface HelpResponse {
  /** 帮助ID */
  id: number
  /** 标题 */
  title: string
  /** 描述 */
  description: string
  /** 图片链接 */
  image_url: string
  /** 排序 */
  sort: number
}

// 帮助信息列表响应
export interface HelpListResponse {
  /** 帮助信息列表 */
  list: HelpResponse[]
}

// 银行列表响应
export interface BankListResponse {
  /** 银行名称列表 */
  banks: string[]
}

// 最近订单响应
export interface RecentOrderResponse {
  /** 面值 */
  face_value: number
  /** 创建时间 */
  create_time: string
  /** 消息 */
  msg: string
  /** 产品名称 */
  pname: string
}

// 最近订单列表响应
export interface RecentOrderListResponse {
  /** 最近订单列表 */
  list: RecentOrderResponse[]
}
