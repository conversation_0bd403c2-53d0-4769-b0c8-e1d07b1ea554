// 消息类型枚举
export enum SystemMessageType {
  SYSTEM = 1, // 系统消息
  ACCOUNT = 2 // 账号消息
}

// 消息响应接口
export interface MessageResponse {
  id: number
  title: string
  description: string
  message_type: SystemMessageType
  user_id: number
  created_at: string
  updated_at: string
}

// 分页消息响应接口
export interface MessagePaginatedResponse {
  list: MessageResponse[]
  page: number
  page_size: number
  total: number
  total_pages: number
}

// 获取消息列表请求参数
export interface GetMessagesParams {
  page?: number
  page_size?: number
}
