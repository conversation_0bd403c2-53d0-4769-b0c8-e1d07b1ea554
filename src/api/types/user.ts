// 用户相关的数据类型定义

// 用户类型枚举
export enum UserType {
  /** 普通用户 */
  Regular = 1,
  /** 会员 */
  VIP = 2
}

// 认证类型枚举
export enum AuthType {
  /** 未认证 */
  None = 1,
  /** 普通认证 */
  Basic = 2,
  /** 高级认证 */
  Advanced = 3
}

// 用户信息响应
export interface UserResponse {
  /** 用户ID */
  id: number
  /** 用户名 */
  username: string
  /** 手机号 */
  phone: string
  /** 微信 OpenID */
  open_id: string
  /** 头像URL */
  avatar: string
  /** 用户类型 1:普通用户 2:会员 */
  user_type: UserType
  /** 认证类型 1:未认证 2:普通认证 3:高级认证 */
  auth_type: AuthType
  /** 真实姓名 */
  real_name?: string
  /** 身份证号码 */
  id_card?: string
  /** 余额 */
  balance: number
  /** 好友数 */
  friend_count: number
  /** 推广收益 */
  promotion_earning: number
  /** 创建时间 */
  created_at: string
}

// 修改用户名请求
export interface UpdateUsernameRequest {
  /** 新用户名 */
  username: string
}

// 实名认证请求
export interface RealNameAuthRequest {
  /** 真实姓名 */
  real_name: string
  /** 身份证号 */
  id_card: string
}

// 修改交易密码请求
export interface UpdateTradePasswordRequest {
  /** 验证码 */
  code: string
  /** 交易密码 6-20位 */
  trade_password: string
}

// 文件上传响应
export interface UploadResponse {
  /** 文件名 */
  filename: string
  /** 文件URL */
  url: string
}
