// 产品相关的数据类型定义

// 产品摘要信息
export interface ProductSummaryResponse {
  /** 产品ID */
  id: number
  /** 产品名称 */
  name: string
  /** 产品图片URL */
  image_url1: string
}

// 产品分类响应
export interface ProductCategoryResponse {
  /** 分类ID */
  id: number
  /** 分类名称 */
  name: string
  /** 该分类下的产品列表 */
  children: ProductSummaryResponse[]
}

// 产品搜索结果项
export interface ProductSearchResponse {
  /** 产品ID */
  id: number
  /** 产品名称 */
  name: string
  /** 产品图片URL */
  image_url1: string
  /** 折扣提示 */
  discount_tip: string
}

// 产品搜索结果列表
export interface ProductSearchListResponse {
  /** 搜索结果列表 */
  list: ProductSearchResponse[]
}

// 热门产品响应
export interface HotProductResponse {
  /** 产品ID */
  id: number
  /** 产品名称 */
  name: string
  /** 产品图片URL */
  image_url1: string
  /** 折扣提示 */
  discount_tip: string
}

// 热门产品列表响应
export interface HotProductListResponse {
  /** 热门产品列表 */
  list: HotProductResponse[]
}

// 分类响应
export interface CategoryResponse {
  /** 分类ID */
  id: number
  /** 分类名称 */
  name: string
}

// 面值响应
export interface FaceValueResponse {
  /** 面值ID */
  id: number
  /** 面值名称 */
  name: string
  /** 面值 */
  value: number
  /** 我的费率 */
  my_rate: string
  /** 我的最小费率 */
  my_min_rate: string
  /** 我的最大费率 */
  my_max_rate: string
  /** 状态 */
  status: boolean
}

// 产品详情响应
export interface ProductDetailResponse {
  /** 产品ID */
  id: number
  /** 产品名称 */
  name: string
  /** 分类ID */
  category_id: number
  /** 分类信息 */
  category: CategoryResponse
  /** 产品图片URL1 */
  image_url1: string
  /** 产品图片URL2 */
  image_url2: string
  /** 折扣提示 */
  discount_tip: string
  /** 卡号规则 */
  card_no_rule: string
  /** 卡密规则 */
  card_pwd_rule: string
  /** 规则描述 */
  rule_desc: string
  /** 规则提示 */
  rule_tip: string
  /** 核销说明 */
  write_off_desc: string
  /** 演示内容 */
  demo: string
  /** 面值列表 */
  face_values: FaceValueResponse[]
}
