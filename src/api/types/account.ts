// 支付宝账户相关类型定义

// 支付宝账户响应
export interface AlipayAccountResponse {
  /** 账户ID */
  id: number
  /** 用户ID */
  user_id: number
  /** 支付宝账户 */
  alipay_account: string
  /** 创建时间 */
  created_at: string
  /** 更新时间 */
  updated_at: string
}

// 支付宝账户列表响应
export interface AlipayAccountListResponse {
  /** 支付宝账户列表 */
  list: AlipayAccountResponse[]
}

// 添加支付宝账户请求
export interface AddAlipayAccountRequest {
  /** 支付宝账户 */
  alipay_account: string
}

// 银行卡相关类型定义

// 银行卡响应
export interface BankCardResponse {
  /** 银行卡ID */
  id: number
  /** 用户ID */
  user_id: number
  /** 银行名称 */
  bank_name: string
  /** 银行账户 */
  bank_account: string
  /** 创建时间 */
  created_at: string
  /** 更新时间 */
  updated_at: string
}

// 银行卡列表响应
export interface BankCardListResponse {
  /** 银行卡列表 */
  list: BankCardResponse[]
}

// 添加银行卡请求
export interface AddBankCardRequest {
  /** 银行名称 */
  bank_name: string
  /** 银行账户 */
  bank_account: string
}

// 删除支付宝账户请求
export interface DeleteAlipayAccountRequest {
  /** 账户ID */
  id: number
  /** 交易密码 */
  trade_password: string
}

// 删除银行卡请求
export interface DeleteBankCardRequest {
  /** 银行卡ID */
  id: number
  /** 交易密码 */
  trade_password: string
}
