// 认证相关的数据类型定义

// 发送短信验证码请求
export interface SendSMSCodeRequest {
  /** 手机号 */
  phone: string
}

// 验证码登录请求
export interface LoginCodeRequest {
  /** 手机号 */
  phone: string
  /** 验证码 */
  code: string
}

// Token 响应
export interface TokenResponse {
  /** JWT Token */
  token: string
}



// 帮助信息响应
export interface HelpResponse {
  /** 帮助ID */
  id: number
  /** 标题 */
  title: string
  /** 描述 */
  description: string
  /** 图片链接 */
  image_url: string
  /** 排序 */
  sort: number
}

// 帮助信息列表响应
export interface HelpListResponse {
  /** 帮助信息列表 */
  list: HelpResponse[]
}
