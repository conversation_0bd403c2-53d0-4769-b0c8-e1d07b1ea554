// 产品相关 API

import request from '../utils/request'
import type { ProductCategoryResponse, ProductSearchListResponse, HotProductListResponse, ProductDetailResponse } from './types/product'

/**
 * 产品相关 API
 */
export const productApi = {
  /**
   * 获取带分类分组的产品信息
   * @description 获取所有分类及其下的产品信息，按分类分组返回
   * @returns Promise<ProductCategoryResponse[]> 分类及产品信息列表
   */
  getProductCategories(): Promise<ProductCategoryResponse[]> {
    return request.get('/products/categories')
  },

  /**
   * 搜索产品
   * @description 根据关键词模糊搜索产品名称，返回匹配的产品列表
   * @param keyword 搜索关键词
   * @returns Promise<ProductSearchListResponse> 搜索结果列表
   */
  searchProducts(keyword: string): Promise<ProductSearchListResponse> {
    return request.get('/products/search', { keyword }, { skipAuth: true })
  },

  /**
   * 获取所有热门产品
   * @description 获取所有热门产品列表，按排序字段排序返回
   * @returns Promise<HotProductListResponse> 热门产品列表
   */
  getHotProducts(): Promise<HotProductListResponse> {
    return request.get('/products/hot', {}, { skipAuth: true })
  },

  /**
   * 根据ID获取产品详情
   * @description 根据产品ID获取产品的详细信息，包含面值信息
   * @param id 产品ID
   * @returns Promise<ProductDetailResponse> 产品详情
   */
  getProductDetail(id: number): Promise<ProductDetailResponse> {
    return request.get(`/products/${id}`, {}, { skipAuth: true })
  }
}
