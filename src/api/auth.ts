import request from '../utils/request'
import type {
  SendSMSCodeRequest,
  LoginCodeRequest,
  TokenResponse
} from './types/auth'

// 认证相关 API
export const authApi = {
  /**
   * 发送短信验证码
   * 向指定手机号发送短信验证码，验证码有效期5分钟，同一手机号1分钟内只能发送一次
   */
  sendSMSCode: (data: SendSMSCodeRequest): Promise<void> => {
    return request.post('/auth/sms/code', data, { skipAuth: true })
  },

  /**
   * 验证码登录
   * 用户使用手机号和验证码登录并获取令牌，如果用户不存在则自动注册
   */
  loginWithCode: (data: LoginCodeRequest): Promise<TokenResponse> => {
    return request.post('/auth/login-code', data, { skipAuth: true })
  }
}


