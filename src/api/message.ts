import request from '../utils/request'
import type {
  MessagePaginatedResponse,
  GetMessagesParams
} from './types/message'

// 消息相关 API
export const messageApi = {
  /**
   * 获取系统消息列表
   * 获取系统消息列表，支持分页
   */
  getSystemMessages: (params?: GetMessagesParams): Promise<MessagePaginatedResponse> => {
    return request.get('/messages/system', {
      params: {
        page: 1,
        page_size: 10,
        ...params
      }
    })
  },

  /**
   * 获取账号消息列表
   * 获取账号消息列表，支持分页
   */
  getAccountMessages: (params?: GetMessagesParams): Promise<MessagePaginatedResponse> => {
    return request.get('/messages/account', {
      params: {
        page: 1,
        page_size: 10,
        ...params
      }
    })
  }
}
