import request from '../utils/request'
import type { SystemConfigResponse, HelpListResponse, BankListResponse, RecentOrderListResponse } from './types/common'

// 通用接口 API
export const commonApi = {
  /**
   * 获取系统配置
   * 获取系统配置信息，包括热门搜索、公告、首页推荐等
   */
  getSystemConfig: (): Promise<SystemConfigResponse> => {
    return request.get('/common/system-config', {}, { skipAuth: true })
  },

  /**
   * 获取所有帮助信息
   * 获取所有帮助信息，按排序字段升序排列
   */
  getHelps: (): Promise<HelpListResponse> => {
    return request.get('/common/helps', {}, { skipAuth: true })
  },

  /**
   * 获取所有银行列表
   * 从系统配置文件中读取所有银行列表信息
   */
  getBanks: (): Promise<BankListResponse> => {
    return request.get('/common/banks', {}, { skipAuth: true })
  },

  /**
   * 获取最近订单
   * 获取最近订单信息，包含面值、时间、消息、产品名称等
   */
  getRecentOrders: (): Promise<RecentOrderListResponse> => {
    return request.get('/common/recent-orders', {}, { skipAuth: true })
  }
}
