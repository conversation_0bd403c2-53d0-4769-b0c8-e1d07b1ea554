import request from '../utils/request'
import type {
  AlipayAccountListResponse,
  AddAlipayAccountRequest,
  DeleteAlipayAccountRequest,
  BankCardListResponse,
  AddBankCardRequest,
  DeleteBankCardRequest
} from './types/account'

// 账户管理相关API
export const accountApi = {
  // 获取所有支付宝账户
  getAllAlipayAccounts: (): Promise<AlipayAccountListResponse> => {
    return request.get('/accounts/alipay')
  },

  // 添加支付宝账户
  addAlipayAccount: (data: AddAlipayAccountRequest): Promise<void> => {
    return request.post('/accounts/alipay', data)
  },

  // 删除支付宝账户
  deleteAlipayAccount: (data: DeleteAlipayAccountRequest): Promise<void> => {
    return request.delete(`/accounts/alipay/${data.id}`, data)
  },

  // 获取所有银行卡
  getAllBankCards: (): Promise<BankCardListResponse> => {
    return request.get('/accounts/bank-card')
  },

  // 添加银行卡
  addBankCard: (data: AddBankCardRequest): Promise<void> => {
    return request.post('/accounts/bank-card', data)
  },

  // 删除银行卡
  deleteBankCard: (data: DeleteBankCardRequest): Promise<void> => {
    return request.delete(`/accounts/bank-card/${data.id}`, data)
  }
}
