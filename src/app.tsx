import { PropsWithChildren } from 'react'
import { useLaunch } from '@tarojs/taro'
import { Provider } from 'react-redux'
import configStore from './store'
import { fetchCurrentUserInfo } from './store/user'
import { fetchSystemConfig } from './store/system'

import './app.scss'

const store = configStore()

function App({ children }: PropsWithChildren<any>) {
  useLaunch(() => {
    // 获取系统配置
    store.dispatch(fetchSystemConfig() as any).catch(() => {
      // 获取系统配置失败时静默处理
    })

    // 获取用户信息
    store.dispatch(fetchCurrentUserInfo() as any).catch(() => {
      // 如果获取失败，fetchCurrentUserInfo内部已经处理了清理工作
    })
  })

  // children 是将要会渲染的页面
  return (
    <Provider store={store}>
      {children}
    </Provider>
  )
}

export default App
