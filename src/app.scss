// 全局样式文件

// 主题色定义
// 使用 :root 和 page 双重定义，确保兼容性
:root {
  --primary-color: #1296db;
  --primary-hover: #0d7bc4;
  --primary-light: #e6f3ff;
  --primary-dark: #0a5a8a;
}

// 小程序专用的 page 级别变量定义
page {
  --primary-color: #1296db;
  --primary-hover: #0d7bc4;
  --primary-light: #e6f3ff;
  --primary-dark: #0a5a8a;
}

// 全局样式类
.theme-primary {
  color: var(--primary-color);
}

.theme-primary-bg {
  background-color: var(--primary-color);
}

.theme-primary-border {
  border-color: var(--primary-color);
}

.theme-primary-hover:hover {
  background-color: var(--primary-hover);
}

// 按钮主题样式
.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--primary-hover);
  }

  &:active {
    background-color: var(--primary-dark);
  }
}

// 链接主题样式
.link-primary {
  color: var(--primary-color);
  text-decoration: none;

  &:hover {
    color: var(--primary-hover);
    text-decoration: underline;
  }
}
