.privacy-policy-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;

  .policy-content {
    flex: 1;
    padding: 32px 24px;
    background: white;
    margin: 16px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    width: calc(100% - 32px);
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
  }

  // 政策头部
  .policy-header {
    margin-bottom: 48px;
    text-align: center;

    .policy-title {
      display: block;
      font-size: 40px;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 24px;
      line-height: 1.3;
    }

    .policy-version {
      display: block;
      font-size: 26px;
      color: #666;
      line-height: 1.5;
      margin-bottom: 8px;
    }
  }

  // 政策章节
  .policy-section {
    margin-bottom: 40px;

    .section-title {
      display: block;
      font-size: 32px;
      font-weight: 600;
      color: var(--primary-color);
      margin-bottom: 24px;
      line-height: 1.4;
      border-left: 6px solid var(--primary-color);
      padding-left: 16px;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
      overflow-wrap: break-word;
    }

    .chapter-title {
      display: block;
      font-size: 34px;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 32px;
      line-height: 1.4;
      border-left: 8px solid var(--primary-color);
      padding-left: 20px;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
      overflow-wrap: break-word;
    }

    .subsection-title {
      display: block;
      font-size: 30px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
      line-height: 1.4;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
      overflow-wrap: break-word;
    }

    .section-content {
      display: block;
      font-size: 28px;
      color: #333;
      line-height: 1.7;
      margin-bottom: 20px;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
      overflow-wrap: break-word;
      max-width: 100%;
    }

    .contact-info {
      display: block;
      font-size: 26px;
      color: #555;
      line-height: 1.6;
      margin-bottom: 12px;
      padding-left: 16px;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
      overflow-wrap: break-word;
      max-width: 100%;
    }

    .important-note {
      display: block;
      font-size: 26px;
      color: #e74c3c;
      line-height: 1.6;
      margin-bottom: 16px;
      padding: 12px;
      background: #fef5f5;
      border-left: 4px solid #e74c3c;
      border-radius: 4px;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
      overflow-wrap: break-word;
      max-width: 100%;
    }

    .list-item {
      display: block;
      font-size: 28px;
      color: #333;
      line-height: 1.6;
      margin-bottom: 12px;
      padding-left: 20px;
      position: relative;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
      overflow-wrap: break-word;
      max-width: 100%;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6px;
        height: 6px;
        background: var(--primary-color);
        border-radius: 50%;
      }
    }
  }

  // 底部间距
  .policy-footer {
    height: 80px;
  }
}

// 滚动条样式优化（仅在支持的平台生效）
.policy-content::-webkit-scrollbar {
  width: 6px;
}

.policy-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.policy-content::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
  opacity: 0.6;
}

.policy-content::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
  opacity: 0.8;
}

// 更小屏幕的适配
@media (max-width: 480px) {
  .privacy-policy-container {
    .policy-content {
      margin: 4px;
      padding: 16px 12px;
      border-radius: 8px;
      width: calc(100% - 8px);
    }

    .policy-header {
      margin-bottom: 24px;

      .policy-title {
        font-size: 32px;
        margin-bottom: 16px;
      }

      .policy-version {
        font-size: 24px;
      }
    }

    .policy-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 28px;
        margin-bottom: 16px;
        border-left-width: 4px;
        padding-left: 12px;
      }

      .chapter-title {
        font-size: 30px;
        margin-bottom: 20px;
        border-left-width: 6px;
        padding-left: 16px;
      }

      .subsection-title {
        font-size: 26px;
        margin-bottom: 16px;
      }

      .section-content {
        font-size: 24px;
        margin-bottom: 16px;
      }

      .contact-info {
        font-size: 22px;
        margin-bottom: 10px;
        padding-left: 12px;
      }

      .important-note {
        font-size: 22px;
        margin-bottom: 12px;
        padding: 10px;
      }

      .list-item {
        font-size: 24px;
        margin-bottom: 10px;
        padding-left: 16px;

        &::before {
          width: 4px;
          height: 4px;
        }
      }
    }

    .policy-footer {
      height: 40px;
    }
  }
}

// 响应式适配
@media (max-width: 750px) and (min-width: 481px) {
  .privacy-policy-container {
    .policy-content {
      margin: 8px;
      padding: 24px 16px;
      border-radius: 12px;
      width: calc(100% - 16px);
      max-width: 100%;
    }

    .policy-header {
      margin-bottom: 32px;

      .policy-title {
        font-size: 36px;
        margin-bottom: 20px;
      }

      .policy-version {
        font-size: 24px;
      }
    }

    .policy-section {
      margin-bottom: 32px;

      .section-title {
        font-size: 30px;
        margin-bottom: 20px;
        border-left-width: 5px;
        padding-left: 14px;
      }

      .chapter-title {
        font-size: 32px;
        margin-bottom: 24px;
        border-left-width: 7px;
        padding-left: 18px;
      }

      .subsection-title {
        font-size: 28px;
        margin-bottom: 18px;
      }

      .section-content {
        font-size: 26px;
        margin-bottom: 18px;
      }

      .contact-info {
        font-size: 24px;
        margin-bottom: 11px;
        padding-left: 14px;
      }

      .important-note {
        font-size: 24px;
        margin-bottom: 14px;
        padding: 11px;
      }

      .list-item {
        font-size: 26px;
        margin-bottom: 11px;
        padding-left: 18px;

        &::before {
          width: 5px;
          height: 5px;
        }
      }
    }

    .policy-footer {
      height: 60px;
    }
  }
}

// 深色模式适配（如果需要）
@media (prefers-color-scheme: dark) {
  .privacy-policy-container {
    background: #1a1a1a;

    .policy-content {
      background: #2d2d2d;
      color: #e0e0e0;
    }

    .policy-header {
      .policy-title {
        color: #ffffff;
      }

      .policy-version {
        color: #b0b0b0;
      }
    }

    .policy-section {
      .section-content {
        color: #e0e0e0;
      }

      .contact-info {
        color: #c0c0c0;
      }

      .important-note {
        color: #ff6b6b;
        background: #2d1b1b;
        border-left-color: #ff6b6b;
      }

      .list-item {
        color: #e0e0e0;
      }

      .subsection-title {
        color: #e0e0e0;
      }
    }
  }
}
