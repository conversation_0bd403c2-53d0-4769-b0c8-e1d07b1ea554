import { useEffect } from 'react'
import { View, Text } from '@tarojs/components'
import { useLoad, useTabItemTap, navigateTo } from '@tarojs/taro'
import { useSelector, useDispatch } from 'react-redux'
import type { RootState } from '../../store'
import { clearUserInfo } from '../../store/user'
import './index.scss'

export default function Order() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo)

  useLoad(() => {
    console.log('Order page loaded.')
  })

  // 监听 tab 切换到订单页面
  useTabItemTap(() => {
    console.log('Tab switched to order page.')
    checkUserLoginStatus()
  })

  // 检查用户登录状态
  useEffect(() => {
    checkUserLoginStatus()
  }, [])

  const checkUserLoginStatus = () => {
    // 检查用户是否登录：userInfo.id 大于0就是登录了
    if (!userInfo || !userInfo.id || userInfo.id <= 0) {
      // 没有登录，跳转到登录页面
      navigateToLogin()
    }
  }

  const navigateToLogin = () => {
    navigateTo({
      url: '/pages/login/index'
    })
  }

  // 如果用户未登录，不显示内容（会自动跳转到登录页面）
  if (!userInfo || !userInfo.id || userInfo.id <= 0) {
    return (
      <View className='order-page'>
        <View className='loading-container'>
          <Text className='loading-text'>跳转到登录页面中...</Text>
        </View>
      </View>
    )
  }

  return (
    <View className='order-page'>
      <View className='header'>
        <Text className='title'>订单管理</Text>
      </View>

      <View className='card'>
        <Text className='card-content'>这里是订单管理页面</Text>
        <Text className='card-desc'>您可以在这里查看和管理您的订单</Text>
        <Text className='welcome-text'>欢迎，{userInfo.username}！</Text>
      </View>
    </View>
  )
}
