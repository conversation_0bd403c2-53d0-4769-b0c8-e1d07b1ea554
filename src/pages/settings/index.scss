.settings-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
}

.settings-section {
  background: white;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;

  &:first-child {
    margin-top: 0;
  }
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 36px 32px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f8f8f8;
  }
}

.setting-left {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.setting-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.setting-title {
  font-size: 34px;
  color: #333;
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 12px;
}

.setting-desc {
  font-size: 26px;
  color: #999;
  line-height: 1.4;
}

.setting-arrow {
  font-size: 36px;
  color: #ddd;
  margin-left: 16px;
}

.setting-value {
  font-size: 28px;
  color: #999;
  margin-left: 16px;
}

/* 信息展示项样式 */
.info-item {
  cursor: default;

  &:active {
    background-color: transparent;
  }

  .setting-title {
    margin-bottom: 0 !important;
  }
}

/* 交易密码项样式 */
.password-item {
  .setting-title {
    margin-bottom: 12px !important;
  }
}

/* 危险操作样式 */
.danger-item {
  .danger-text {
    color: #ff4757;
  }

  .danger-desc {
    color: #ff6b7a;
  }
}

/* 退出登录特殊样式 */
.setting-item:last-child {
  .setting-title {
    margin-bottom: 0;
    text-align: left;
  }
}

/* 响应式调整 */
@media (max-width: 750px) {
  .setting-item {
    padding: 36px 32px;
  }

  .setting-title {
    font-size: 30px;
  }

  .setting-desc {
    font-size: 22px;
  }

  .setting-value {
    font-size: 26px;
  }

  .setting-arrow {
    font-size: 32px;
  }
}
