import { View, Text } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import { useSelector, useDispatch } from 'react-redux'
import Taro from '@tarojs/taro'
import type { RootState } from '../../store'
import { clearUserInfo } from '../../store/user'
import { userApi } from '../../api/user'
import request from '../../utils/request'
import './index.scss'

export default function Settings() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo)
  const dispatch = useDispatch()

  useLoad(() => {
    console.log('Settings page loaded.')
  })

  // 处理菜单项点击
  const handleMenuClick = (type: string) => {
    switch (type) {
      case 'basic':
        Taro.navigateTo({
          url: '/pages/basic-info/index'
        })
        break
      case 'realname':
        Taro.navigateTo({
          url: '/pages/identity-verification/index'
        })
        break
      case 'withdraw':
        Taro.navigateTo({
          url: '/pages/withdraw-accounts/index'
        })
        break
      case 'password':
        Taro.navigateTo({
          url: '/pages/trade-password/index'
        })
        break
      case 'delete':
        Taro.showModal({
          title: '注销账号',
          content: '注销后将清空用户数据，此操作不可恢复，确定要注销吗？',
          confirmText: '确定注销',
          confirmColor: '#ff4757',
          success: async (res) => {
            if (res.confirm) {
              try {
                // 显示加载提示
                Taro.showLoading({
                  title: '注销中...'
                })

                // 调用注销账户接口
                await userApi.deleteAccount()

                Taro.hideLoading()

                // 清除本地token缓存
                request.clearAuth()

                // 清空store用户信息
                dispatch(clearUserInfo())

                Taro.showToast({
                  title: '注销成功',
                  icon: 'success'
                })

                setTimeout(() => {
                  // 跳转到用户页
                  Taro.switchTab({
                    url: '/pages/user/index'
                  })
                }, 1500)
              } catch (error) {
                Taro.hideLoading()

                const errorMessage = error instanceof Error ? error.message : '注销失败，请重试'
                Taro.showToast({
                  title: errorMessage,
                  icon: 'none'
                })
              }
            }
          }
        })
        break
      case 'logout':
        Taro.showModal({
          title: '退出登录',
          content: '确认退出登录程序!',
          cancelText: '取消',
          confirmText: '确定',
          confirmColor: '#1890ff',
          success: async (res) => {
            if (res.confirm) {
              try {
                // 调用退出登录接口（不管成功失败都继续执行后续操作）
                await userApi.logout().catch(() => {
                  // 忽略接口错误，继续执行本地清理
                })

                // 清除本地token缓存
                request.clearAuth()

                // 清空store用户信息
                dispatch(clearUserInfo())

                // 跳转到用户页（tabbar页面使用switchTab）
                Taro.switchTab({
                  url: '/pages/user/index'
                })
              } catch (error) {
                // 即使出错也要清理本地数据
                request.clearAuth()
                dispatch(clearUserInfo())

                Taro.switchTab({
                  url: '/pages/user/index'
                })
              }
            }
          }
        })
        break
      default:
        break
    }
  }

  // 格式化注册时间
  const formatRegisterTime = (timeStr: string) => {
    if (!timeStr) return '--'
    const date = new Date(timeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  // 格式化手机号（中间4位显示为*）
  const formatPhone = (phone: string) => {
    if (!phone) return '--'
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }

  return (
    <View className='settings-page'>
      {/* 可点击的设置项 */}
      <View className='settings-section'>
        <View className='setting-item' onClick={() => handleMenuClick('basic')}>
          <View className='setting-left'>
            <Text className='setting-title'>基本信息</Text>
            <Text className='setting-desc'>用户进行系统昵称的修改</Text>
          </View>
          <View className='setting-arrow'>›</View>
        </View>

        <View className='setting-item' onClick={() => handleMenuClick('realname')}>
          <View className='setting-left'>
            <Text className='setting-title'>实名认证</Text>
            <Text className='setting-desc'>防止虚假信息交易</Text>
          </View>
          <View className='setting-arrow'>›</View>
        </View>

        <View className='setting-item' onClick={() => handleMenuClick('withdraw')}>
          <View className='setting-left'>
            <Text className='setting-title'>提现账户</Text>
            <Text className='setting-desc'>支付宝提现、银行卡提现</Text>
          </View>
          <View className='setting-arrow'>›</View>
        </View>

        <View className='setting-item password-item' onClick={() => handleMenuClick('password')}>
          <View className='setting-left'>
            <Text className='setting-title'>交易密码</Text>
            <Text className='setting-desc'>为了您的资金安全，请设置交易密码</Text>
          </View>
          <View className='setting-arrow'>›</View>
        </View>
      </View>

      {/* 信息展示项和账号操作 */}
      <View className='settings-section'>
        <View className='setting-item info-item'>
          <Text className='setting-title'>注册手机</Text>
          <Text className='setting-value'>{(userInfo && userInfo.phone) || '--'}</Text>
        </View>

        <View className='setting-item info-item'>
          <Text className='setting-title'>注册时间</Text>
          <Text className='setting-value'>{formatRegisterTime((userInfo && userInfo.created_at) || '')}</Text>
        </View>

        <View className='setting-item danger-item' onClick={() => handleMenuClick('delete')}>
          <Text className='setting-title danger-text'>注销账号</Text>
          <View className='setting-right'>
            <Text className='setting-desc danger-desc'>注销后清空用户数据</Text>
            <View className='setting-arrow'>›</View>
          </View>
        </View>

        <View className='setting-item' onClick={() => handleMenuClick('logout')}>
          <Text className='setting-title'>退出登录</Text>
          <View className='setting-arrow'>›</View>
        </View>
      </View>
    </View>
  )
}
