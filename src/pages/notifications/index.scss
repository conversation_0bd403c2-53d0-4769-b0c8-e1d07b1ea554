.notifications {
  min-height: 100vh;
  background-color: #f5f5f5;

  .tab-bar {
    display: flex;
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;

    .tab-item {
      flex: 1;
      position: relative;
      padding: 32rpx 0;
      text-align: center;
      cursor: pointer;

      .tab-text {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
      }

      &.active {
        .tab-text {
          color: var(--primary-color);
          font-weight: 600;
        }
      }

      .tab-indicator {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 6rpx;
        background-color: var(--primary-color);
        border-radius: 3rpx;
      }
    }
  }

  .message-list {
    height: calc(100vh - 120rpx);
    padding: 20rpx;

    .message-item {
      background-color: #fff;
      border-radius: 16rpx;
      padding: 32rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

      .message-header {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .message-icon {
          width: 48rpx;
          height: 48rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 24rpx;
          flex-shrink: 0;

          .icon-text {
            font-size: 28rpx;
            color: #52c41a;
          }

          .icon-image {
            width: 48rpx;
            height: 48rpx;
          }
        }

        .message-title {
          flex: 1;
          font-size: 28rpx;
          font-weight: 600;
          color: #262626;
          line-height: 1.4;
          margin-right: 20rpx;
        }

        .message-time {
          font-size: 22rpx;
          color: #999;
          white-space: nowrap;
        }
      }

      .message-content-wrapper {
        display: flex;
        align-items: flex-start;

        .message-icon-placeholder {
          width: 48rpx;
          height: 1rpx; /* 最小高度，只用于占位 */
          margin-right: 24rpx;
          flex-shrink: 0;
        }

        .message-text {
          flex: 1;
          font-size: 26rpx;
          color: #666;
          line-height: 1.5;
          word-break: break-all;
        }
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400rpx;

      .empty-text {
        font-size: 26rpx;
        color: #999;
      }
    }

    .loading-more {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 32rpx;

      .loading-text {
        font-size: 26rpx;
        color: #999;
      }
    }

    .no-more {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 32rpx;

      .no-more-text {
        font-size: 22rpx;
        color: #ccc;
      }
    }
  }
}


