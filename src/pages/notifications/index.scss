.notifications {
  min-height: 100vh;
  background-color: #f5f5f5;

  .tab-bar {
    display: flex;
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;

    .tab-item {
      flex: 1;
      position: relative;
      padding: 32rpx 0;
      text-align: center;
      cursor: pointer;

      .tab-text {
        font-size: 32rpx;
        color: #666;
        font-weight: 500;
      }

      &.active {
        .tab-text {
          color: var(--primary-color);
          font-weight: 600;
        }
      }

      .tab-indicator {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 6rpx;
        background-color: var(--primary-color);
        border-radius: 3rpx;
      }
    }
  }

  .message-list {
    height: calc(100vh - 120rpx);
    padding: 20rpx;

    .message-item {
      background-color: #fff;
      border-radius: 16rpx;
      padding: 32rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

      .message-header {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .message-icon {
          width: 48rpx;
          height: 48rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 24rpx;
          flex-shrink: 0;

          .icon-text {
            font-size: 32rpx;
            color: #52c41a;
          }

          .icon-image {
            width: 48rpx;
            height: 48rpx;
          }
        }

        .message-title {
          flex: 1;
          font-size: 32rpx;
          font-weight: 600;
          color: #262626;
          line-height: 1.4;
          margin-right: 20rpx;
        }

        .message-time {
          font-size: 24rpx;
          color: #999;
          white-space: nowrap;
        }
      }

      .message-text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
        word-break: break-all;
        margin-left: 72rpx; /* 与图标对齐 */
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400rpx;

      .empty-text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .notifications {
    .message-list {
      .message-item {
        padding: 24rpx;

        .message-header {
          .message-icon {
            width: 40rpx;
            height: 40rpx;
            margin-right: 16rpx;

            .icon-text {
              font-size: 24rpx;
            }

            .icon-image {
              width: 40rpx;
              height: 40rpx;
            }
          }

          .message-title {
            font-size: 28rpx;
          }

          .message-time {
            font-size: 22rpx;
          }
        }

        .message-text {
          font-size: 26rpx;
          margin-left: 56rpx; /* 调整对齐 */
        }
      }
    }
  }
}
