import { useState } from 'react'
import { View, Text, ScrollView } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.scss'

interface NotificationItem {
  id: string
  title: string
  content: string
  time: string
  type: 'system' | 'account'
  icon?: string
}

const Notifications = () => {
  const [activeTab, setActiveTab] = useState<'system' | 'account'>('account')

  // 模拟数据
  const systemMessages: NotificationItem[] = [
    {
      id: '1',
      title: '请确保充值卡的来源合法，京顺回收拒绝一切违法行为',
      content: '请确保充值卡的来源合法，京顺回收拒绝一切通过传销、诈骗、洗钱等非法手段获取的充值卡。一经发现异常情况，京顺回收有义务向公安机关反映。',
      time: '2024-09-15',
      type: 'system',
      icon: '💬'
    },
    {
      id: '2',
      title: '2024年9月14日中石化充值卡恢复处理',
      content: '2024年9月14日中石化充值卡恢复处理，全面值1-10分钟内结算',
      time: '2024-09-14',
      type: 'system',
      icon: '💬'
    },
    {
      id: '3',
      title: '声明：京顺回收无任何代理，说代理的都属于诈骗',
      content: '声明：京顺回收无任何代理，说代理的都属于诈骗！对所有知悉侵权行为的企业和个人具有法律约束力，若因忽视本声明导致损失，相关责任将自行承担。',
      time: '2024-09-13',
      type: 'system',
      icon: '💬'
    },
    {
      id: '4',
      title: '新增产品：和信通礼品卡，当当礼品卡，智慧卡，关爱通积分卡',
      content: '新增产品：和信通礼品卡，当当礼品卡，智慧卡，关爱通积分卡，双购通购物卡，屈臣氏e-fun卡，欢迎新老用户提交！',
      time: '2024-09-12',
      type: 'system',
      icon: '💬'
    }
  ]

  const accountMessages: NotificationItem[] = [
    {
      id: '5',
      title: '账户余额变动通知',
      content: '您的账户余额发生变动，当前余额：¥1,234.56',
      time: '2024-09-15',
      type: 'account',
      icon: '💰'
    },
    {
      id: '6',
      title: '提现申请已处理',
      content: '您申请的提现已成功处理，金额：¥500.00，预计1-3个工作日到账',
      time: '2024-09-14',
      type: 'account',
      icon: '💰'
    }
  ]

  const currentMessages = activeTab === 'system' ? systemMessages : accountMessages

  const handleTabChange = (tab: 'system' | 'account') => {
    setActiveTab(tab)
  }

  return (
    <View className='notifications'>
      {/* 标签栏 */}
      <View className='tab-bar'>
        <View
          className={`tab-item ${activeTab === 'account' ? 'active' : ''}`}
          onClick={() => handleTabChange('account')}
        >
          <Text className='tab-text'>账号消息</Text>
          {activeTab === 'account' && <View className='tab-indicator' />}
        </View>
        <View
          className={`tab-item ${activeTab === 'system' ? 'active' : ''}`}
          onClick={() => handleTabChange('system')}
        >
          <Text className='tab-text'>系统消息</Text>
          {activeTab === 'system' && <View className='tab-indicator' />}
        </View>
      </View>

      {/* 消息列表 */}
      <ScrollView className='message-list' scrollY>
        {currentMessages.map((message) => (
          <View key={message.id} className='message-item'>
            <View className='message-icon'>
              <Text className='icon-text'>{message.icon}</Text>
            </View>
            <View className='message-content'>
              <View className='message-header'>
                <Text className='message-title'>{message.title}</Text>
                <Text className='message-time'>{message.time}</Text>
              </View>
              <Text className='message-text'>{message.content}</Text>
            </View>
          </View>
        ))}

        {currentMessages.length === 0 && (
          <View className='empty-state'>
            <Text className='empty-text'>暂无消息</Text>
          </View>
        )}
      </ScrollView>
    </View>
  )
}

export default Notifications
