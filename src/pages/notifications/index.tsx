import { useState, useEffect } from 'react'
import { View, Text, ScrollView, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { messageApi, SystemMessageType } from '../../api'
import type { MessageResponse } from '../../api'
import accountMessageIcon from '../../assets/images/account_message_icon.png'
import systemMessageIcon from '../../assets/images/system_message_icon.png'
import './index.scss'

interface NotificationItem {
  id: number
  title: string
  content: string
  time: string
  type: 'system' | 'account'
  icon?: string
}

const Notifications = () => {
  const [activeTab, setActiveTab] = useState<'system' | 'account'>('account')
  const [systemMessages, setSystemMessages] = useState<NotificationItem[]>([])
  const [accountMessages, setAccountMessages] = useState<NotificationItem[]>([])
  const [loading, setLoading] = useState(false)
  const [systemPage, setSystemPage] = useState(1)
  const [accountPage, setAccountPage] = useState(1)
  const [hasMoreSystem, setHasMoreSystem] = useState(true)
  const [hasMoreAccount, setHasMoreAccount] = useState(true)

  // 转换API响应数据为组件需要的格式
  const transformMessage = (message: MessageResponse): NotificationItem => {
    return {
      id: message.id,
      title: message.title,
      content: message.description,
      time: formatTime(message.created_at),
      type: message.message_type === SystemMessageType.SYSTEM ? 'system' : 'account',
      icon: message.message_type === SystemMessageType.SYSTEM ? 'system' : 'account'
    }
  }

  // 格式化时间
  const formatTime = (timeStr: string): string => {
    try {
      const date = new Date(timeStr)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).replace(/\//g, '-')
    } catch (error) {
      return timeStr
    }
  }

  // 获取系统消息
  const fetchSystemMessages = async (page: number = 1, append: boolean = false) => {
    try {
      setLoading(true)
      console.log('开始获取系统消息，页码:', page)
      const response = await messageApi.getSystemMessages({
        page,
        page_size: 10
      })

      console.log('系统消息API响应:', response)

      const transformedMessages = response.list.map(transformMessage)
      console.log('转换后的系统消息:', transformedMessages)

      if (append) {
        setSystemMessages(prev => [...prev, ...transformedMessages])
      } else {
        setSystemMessages(transformedMessages)
      }

      setHasMoreSystem(page < response.total_pages)
    } catch (error) {
      console.error('获取系统消息失败，详细错误:', error)
      console.error('错误类型:', typeof error)
      console.error('错误信息:', error?.message || error)
      Taro.showToast({
        title: '获取系统消息失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 获取账号消息
  const fetchAccountMessages = async (page: number = 1, append: boolean = false) => {
    try {
      setLoading(true)
      console.log('开始获取账号消息，页码:', page)
      const response = await messageApi.getAccountMessages({
        page,
        page_size: 10
      })

      console.log('账号消息API响应:', response)

      const transformedMessages = response.list.map(transformMessage)
      console.log('转换后的账号消息:', transformedMessages)

      if (append) {
        setAccountMessages(prev => [...prev, ...transformedMessages])
      } else {
        setAccountMessages(transformedMessages)
      }

      setHasMoreAccount(page < response.total_pages)
    } catch (error) {
      console.error('获取账号消息失败，详细错误:', error)
      console.error('错误类型:', typeof error)
      console.error('错误信息:', error?.message || error)
      Taro.showToast({
        title: '获取账号消息失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 初始化数据
  useEffect(() => {
    fetchSystemMessages(1)
    fetchAccountMessages(1)
  }, [])

  const currentMessages = activeTab === 'system' ? systemMessages : accountMessages

  const handleTabChange = (tab: 'system' | 'account') => {
    setActiveTab(tab)
  }

  // 加载更多数据
  const loadMore = () => {
    if (loading) return

    if (activeTab === 'system' && hasMoreSystem) {
      const nextPage = systemPage + 1
      setSystemPage(nextPage)
      fetchSystemMessages(nextPage, true)
    } else if (activeTab === 'account' && hasMoreAccount) {
      const nextPage = accountPage + 1
      setAccountPage(nextPage)
      fetchAccountMessages(nextPage, true)
    }
  }

  // 下拉刷新
  const onRefresh = () => {
    if (activeTab === 'system') {
      setSystemPage(1)
      fetchSystemMessages(1)
    } else {
      setAccountPage(1)
      fetchAccountMessages(1)
    }
  }

  return (
    <View className='notifications'>
      {/* 标签栏 */}
      <View className='tab-bar'>
        <View
          className={`tab-item ${activeTab === 'account' ? 'active' : ''}`}
          onClick={() => handleTabChange('account')}
        >
          <Text className='tab-text'>账号消息</Text>
          {activeTab === 'account' && <View className='tab-indicator' />}
        </View>
        <View
          className={`tab-item ${activeTab === 'system' ? 'active' : ''}`}
          onClick={() => handleTabChange('system')}
        >
          <Text className='tab-text'>系统消息</Text>
          {activeTab === 'system' && <View className='tab-indicator' />}
        </View>
      </View>

      {/* 消息列表 */}
      <ScrollView
        className='message-list'
        scrollY
        refresherEnabled
        refresherTriggered={loading}
        onRefresherRefresh={onRefresh}
        onScrollToLower={loadMore}
        lowerThreshold={100}
      >
        {currentMessages.map((message) => (
          <View key={message.id} className='message-item'>
            <View className='message-header'>
              <View className='message-icon'>
                {message.icon === 'account' ? (
                  <Image
                    className='icon-image'
                    src={accountMessageIcon}
                    mode='aspectFit'
                  />
                ) : message.icon === 'system' ? (
                  <Image
                    className='icon-image'
                    src={systemMessageIcon}
                    mode='aspectFit'
                  />
                ) : (
                  <Text className='icon-text'>{message.icon}</Text>
                )}
              </View>
              <Text className='message-title'>{message.title}</Text>
              <Text className='message-time'>{message.time}</Text>
            </View>
            <View className='message-content-wrapper'>
              <View className='message-icon-placeholder'></View>
              <Text className='message-text'>{message.content}</Text>
            </View>
          </View>
        ))}

        {/* 加载状态 */}
        {loading && currentMessages.length > 0 && (
          <View className='loading-more'>
            <Text className='loading-text'>加载中...</Text>
          </View>
        )}

        {/* 没有更多数据 */}
        {!loading && currentMessages.length > 0 &&
         ((activeTab === 'system' && !hasMoreSystem) ||
          (activeTab === 'account' && !hasMoreAccount)) && (
          <View className='no-more'>
            <Text className='no-more-text'>没有更多消息了</Text>
          </View>
        )}

        {/* 空状态 */}
        {!loading && currentMessages.length === 0 && (
          <View className='empty-state'>
            <Text className='empty-text'>暂无消息</Text>
          </View>
        )}
      </ScrollView>
    </View>
  )
}

export default Notifications
