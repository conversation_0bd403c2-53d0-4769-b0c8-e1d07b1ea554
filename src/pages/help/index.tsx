import { View, Text } from '@tarojs/components'
import { useState, useEffect } from 'react'
import { useLoad } from '@tarojs/taro'
import Taro from '@tarojs/taro'
import { commonApi } from '../../api/common'
import type { HelpResponse } from '../../api/types/common'
import './index.scss'

export default function Help() {
  const [helpList, setHelpList] = useState<HelpResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [selectedHelp, setSelectedHelp] = useState<HelpResponse | null>(null)

  useLoad(() => {
    console.log('Help page loaded.')
    fetchHelpList()
  })

  // 获取帮助信息列表
  const fetchHelpList = async () => {
    try {
      setLoading(true)
      const response = await commonApi.getHelps()
      setHelpList(response.list)
    } catch (error) {
      console.error('获取帮助信息失败:', error)
      Taro.showToast({
        title: '获取帮助信息失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 点击帮助项目
  const handleHelpClick = (help: HelpResponse) => {
    setSelectedHelp(help)
    setShowModal(true)
  }

  // 关闭弹窗
  const closeModal = () => {
    setShowModal(false)
    setSelectedHelp(null)
  }

  // 在线客服
  const handleOnlineService = () => {
    Taro.showToast({
      title: '在线客服功能开发中',
      icon: 'none'
    })
  }

  return (
    <View className='help-page'>
      {/* 帮助列表 */}
      <View className='help-list'>
        {loading ? (
          <View className='loading'>
            <Text>加载中...</Text>
          </View>
        ) : (
          helpList.map((help, index) => (
            <View
              key={help.id}
              className='help-item'
              onClick={() => handleHelpClick(help)}
            >
              <Text className='help-title'>{help.title}</Text>
              <View className='help-arrow'>›</View>
            </View>
          ))
        )}
      </View>

      {/* 在线客服按钮 */}
      <View className='online-service-btn' onClick={handleOnlineService}>
        <View className='service-icon'>💬</View>
        <Text className='service-text'>在线客服</Text>
      </View>

      {/* 帮助详情弹窗 */}
      {showModal && selectedHelp && (
        <View className='modal-overlay' onClick={closeModal}>
          <View className='modal-content' onClick={(e) => e.stopPropagation()}>
            <View className='modal-header'>
              <Text className='modal-title'>在线帮助</Text>
              <View className='close-btn' onClick={closeModal}>×</View>
            </View>
            <View className='modal-body'>
              <Text className='help-question'>{selectedHelp.title}</Text>
              <Text className='help-answer'>{selectedHelp.description}</Text>
            </View>
          </View>
        </View>
      )}
    </View>
  )
}
