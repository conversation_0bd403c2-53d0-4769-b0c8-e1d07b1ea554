.help-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120px;
}

/* 帮助列表 */
.help-list {
  background: white;
  margin: 24px 32px;
  border-radius: 16px;
  overflow: hidden;
}

.loading {
  padding: 80px;
  text-align: center;
  color: #999;
  font-size: 28px;
}

.help-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.help-item:last-child {
  border-bottom: none;
}

.help-item:active {
  background-color: #f8f8f8;
}

.help-title {
  flex: 1;
  font-size: 28px;
  color: #333;
  font-weight: bold;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.help-arrow {
  font-size: 32px;
  color: #ccc;
  margin-left: 16px;
}

/* 在线客服按钮 */
.online-service-btn {
  position: fixed;
  bottom: 32px;
  left: 32px;
  right: 32px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border-radius: 48px;
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 8px 24px rgba(18, 150, 219, 0.3);
  transition: transform 0.2s ease;
}

.online-service-btn:active {
  transform: scale(0.98);
}

.service-icon {
  font-size: 32px;
  margin-right: 16px;
}

.service-text {
  color: white;
  font-size: 32px;
  font-weight: bold;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 24px 24px 0 0;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.modal-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
}

.close-btn {
  position: absolute;
  right: 32px;
  top: 50%;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #999;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.close-btn:active {
  background-color: #e0e0e0;
}

.modal-body {
  padding: 32px 32px 200px 32px;
  max-height: 60vh;
  overflow-y: auto;
}

.help-question {
  display: block;
  font-size: 28px;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 24px;
  line-height: 1.4;
  position: relative;
  padding-left: 24px;
}

.help-question::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 24px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.help-answer {
  display: block;
  font-size: 26px;
  color: #333;
  line-height: 1.6;
}
