.card-page {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止页面整体滚动 */
  position: relative; /* 确保定位正确 */
}



/* 主体内容 */
.main-content {
  flex: 1;
  display: flex;
  height: calc(100vh - 112px); /* 减去搜索栏高度 */
  overflow: hidden; /* 防止主体内容区域滚动 */
  min-height: 0; /* 确保flex子元素可以缩小 */
}

/* 左侧分类导航 */
.left-sidebar {
  width: 160px;
  background: #f8f8f8;
  height: 100%; /* 占满主体内容区域的高度 */
  flex-shrink: 0; /* 防止左侧栏被压缩 */
}

.category-item {
  padding: 32px 24px;
  position: relative;
  text-align: center;
  font-size: 28px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 700;

  &:hover:not(.active) {
    background: #fafafa;
  }
}

.category-item.active {
  background: var(--primary-light);
  color: var(--primary-color);
  font-weight: 600;
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6px;
  background: var(--primary-color);
}

.category-name {
  font-size: 28px;
  font-weight: 500;
}

/* 右侧产品列表 */
.right-content {
  flex: 1;
  background: white;
  padding: 0 24px;
  height: 100%; /* 占满主体内容区域的高度 */
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 28px;
  color: #999;
}

.error-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  gap: 24px;
}

.error-text {
  font-size: 28px;
  color: #999;
}

.retry-btn {
  background: #ff4444;
  color: white;
  padding: 16px 32px;
  border-radius: 24px;
  font-size: 28px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.empty-text {
  font-size: 28px;
  color: #999;
}

.category-section {
  min-height: 400px; /* 确保每个分类区域有足够的高度 */
}

.category-title {
  padding: 32px 60px 24px; /* 增加左右内边距，让线条变短 */
  font-size: 28px;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #f0f0f0;
  }

  &::before {
    margin-right: 40px;
  }

  &::after {
    margin-left: 40px;
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  padding-bottom: 32px;
}

.product-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 16px;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
}

.placeholder-text {
  font-size: 48px;
  opacity: 0.5;
}

.product-name {
  font-size: 24px;
  color: #333;
  line-height: 1.4;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
