.user-agreement-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;

  .agreement-content {
    flex: 1;
    padding: 32px 24px;
    background: white;
    margin: 16px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    width: calc(100% - 32px);
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
  }

  // 协议头部
  .agreement-header {
    margin-bottom: 48px;
    text-align: center;

    .agreement-title {
      display: block;
      font-size: 40px;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 24px;
      line-height: 1.3;
    }

    .agreement-subtitle {
      display: block;
      font-size: 28px;
      color: #666;
      line-height: 1.6;
      text-align: left;
      margin-top: 16px;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
      overflow-wrap: break-word;
    }
  }

  // 协议章节
  .agreement-section {
    margin-bottom: 48px;

    .section-title {
      display: block;
      font-size: 32px;
      font-weight: 600;
      color: var(--primary-color);
      margin-bottom: 32px;
      line-height: 1.4;
      border-left: 6px solid var(--primary-color);
      padding-left: 16px;
    }

    // 章节条目
    .section-item {
      display: flex;
      margin-bottom: 24px;
      line-height: 1.6;

      .item-number {
        font-size: 28px;
        font-weight: 600;
        color: var(--primary-color);
        margin-right: 12px;
        flex-shrink: 0;
        min-width: 32px;
      }

      .item-content {
        flex: 1;
        font-size: 28px;
        color: #333;
        line-height: 1.7;
        word-wrap: break-word;
        word-break: break-all;
        white-space: pre-wrap;
        overflow-wrap: break-word;
        max-width: 100%;
      }
    }

    // 子条目
    .subsection-item {
      margin-left: 44px;
      margin-bottom: 16px;

      .subsection-content {
        display: block;
        font-size: 26px;
        color: #555;
        line-height: 1.6;
        padding-left: 16px;
        position: relative;
        word-wrap: break-word;
        word-break: break-all;
        white-space: pre-wrap;
        overflow-wrap: break-word;
        max-width: 100%;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 6px;
          height: 6px;
          background: var(--primary-color);
          border-radius: 50%;
        }
      }
    }
  }

  // 底部间距
  .agreement-footer {
    height: 80px;
  }
}

// 滚动条样式优化（仅在支持的平台生效）
.agreement-content::-webkit-scrollbar {
  width: 6px;
}

.agreement-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.agreement-content::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
  opacity: 0.6;
}

.agreement-content::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
  opacity: 0.8;
}

// 更小屏幕的适配
@media (max-width: 480px) {
  .user-agreement-container {
    .agreement-content {
      margin: 4px;
      padding: 16px 12px;
      border-radius: 8px;
      width: calc(100% - 8px);
    }

    .agreement-header {
      margin-bottom: 24px;

      .agreement-title {
        font-size: 32px;
        margin-bottom: 12px;
      }

      .agreement-subtitle {
        font-size: 24px;
        line-height: 1.5;
      }
    }

    .agreement-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 28px;
        margin-bottom: 16px;
        border-left-width: 3px;
        padding-left: 10px;
      }

      .section-item {
        margin-bottom: 16px;

        .item-number {
          font-size: 24px;
          min-width: 24px;
          margin-right: 8px;
        }

        .item-content {
          font-size: 24px;
          line-height: 1.6;
        }
      }

      .subsection-item {
        margin-left: 28px;
        margin-bottom: 10px;

        .subsection-content {
          font-size: 22px;
          padding-left: 10px;

          &::before {
            width: 3px;
            height: 3px;
          }
        }
      }
    }

    .agreement-footer {
      height: 40px;
    }
  }
}

// 响应式适配
@media (max-width: 750px) and (min-width: 481px) {
  .user-agreement-container {
    .agreement-content {
      margin: 8px;
      padding: 24px 16px;
      border-radius: 12px;
      width: calc(100% - 16px);
      max-width: 100%;
    }

    .agreement-header {
      margin-bottom: 32px;

      .agreement-title {
        font-size: 36px;
        margin-bottom: 16px;
      }

      .agreement-subtitle {
        font-size: 26px;
        word-wrap: break-word;
        word-break: break-all;
        white-space: pre-wrap;
        overflow-wrap: break-word;
      }
    }

    .agreement-section {
      margin-bottom: 32px;

      .section-title {
        font-size: 30px;
        margin-bottom: 24px;
        border-left-width: 4px;
        padding-left: 12px;
      }

      .section-item {
        margin-bottom: 20px;

        .item-number {
          font-size: 26px;
          min-width: 28px;
        }

        .item-content {
          font-size: 26px;
          word-wrap: break-word;
          word-break: break-all;
          white-space: pre-wrap;
          overflow-wrap: break-word;
          max-width: 100%;
        }
      }

      .subsection-item {
        margin-left: 36px;
        margin-bottom: 12px;

        .subsection-content {
          font-size: 24px;
          padding-left: 12px;
          word-wrap: break-word;
          word-break: break-all;
          white-space: pre-wrap;
          overflow-wrap: break-word;
          max-width: 100%;

          &::before {
            width: 4px;
            height: 4px;
          }
        }
      }
    }

    .agreement-footer {
      height: 60px;
    }
  }
}

// 深色模式适配（如果需要）
@media (prefers-color-scheme: dark) {
  .user-agreement-container {
    background: #1a1a1a;

    .agreement-content {
      background: #2d2d2d;
      color: #e0e0e0;
    }

    .agreement-header {
      .agreement-title {
        color: #ffffff;
      }

      .agreement-subtitle {
        color: #b0b0b0;
      }
    }

    .agreement-section {
      .section-item {
        .item-content {
          color: #e0e0e0;
        }
      }

      .subsection-item {
        .subsection-content {
          color: #c0c0c0;
        }
      }
    }
  }
}
