.search-page {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;

  .search-bar-wrapper {
    // 覆盖SearchBar的默认样式，保持原有样式
    :global(.search-bar) {
      background: white;
      border-bottom: 1px solid #f0f0f0;
      margin: 0;
      padding: 20px 24px;
      border-radius: 0;
    }
  }

  .content-area {
    background: #f5f5f5;
    min-height: calc(100vh - 112px);
    padding: 16px 0;
  }

  .hot-search-section {
    background: white;
    border-radius: 0;
    padding: 32px;
    margin-bottom: 16px;

    .section-title {
      font-size: 28px;
      font-weight: 600;
      color: #333;
      margin-bottom: 60px;
    }

    .hot-search-tags {
      display: flex !important;
      flex-wrap: wrap !important;
      justify-content: flex-start;
      width: 100%;
      margin-top: 40px;

      .hot-tag {
        color: #666;
        padding: 12px 0;
        border-radius: 24px;
        font-size: 28px;
        border: none;
        background: transparent;
        width: calc(50% - 8px) !important;
        max-width: calc(50% - 8px);
        text-align: left;
        margin-right: 16px;
        margin-bottom: 16px;
        box-sizing: border-box;
        flex-shrink: 0;

        &:nth-child(2n) {
          margin-right: 0;
        }

        &:active {
          background: #f5f5f5;
        }
      }
    }
  }

  .search-results {
    .results-header {
      background: white;
      padding: 24px 32px;
      border-radius: 0;
      border-bottom: 1px solid #f0f0f0;
      text-align: center;

      .results-count {
        font-size: 28px;
        color: #333;
        font-weight: 600;
      }
    }

    .results-list {
      background: white;
      border-radius: 0;

      .result-item {
        display: flex;
        align-items: center;
        padding: 24px 32px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .product-image {
          width: 80px;
          height: 80px;
          border-radius: 12px;
          margin-right: 24px;
          background: #f5f5f5;
        }

        .product-info {
          flex: 1;

          .product-name {
            font-size: 28px;
            color: #333;
            margin-bottom: 12px;
            font-weight: 600;
          }

          .product-discount {
            font-size: 24px;
            color: #ff4d4f;
            background: #fff2f0;
            padding: 6px 12px;
            border-radius: 8px;
            display: inline-block;
          }
        }

        .arrow-icon {
          width: 32px;
          height: 32px;
          color: #ccc;
          font-size: 32px;
        }
      }
    }
  }

  .loading {
    text-align: center;
    padding: 80px;
    color: #666;
    font-size: 28px;
  }

  .empty {
    text-align: center;
    padding: 120px 32px;
    color: #999;

    .empty-text {
      font-size: 32px;
      margin-bottom: 16px;
    }

    .empty-desc {
      font-size: 28px;
      color: #ccc;
    }
  }
}
