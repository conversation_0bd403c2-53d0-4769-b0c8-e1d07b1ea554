import { View, Text, Image } from '@tarojs/components'
import { useLoad, useRouter, navigateTo } from '@tarojs/taro'
import { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import SearchBar from '@/components/SearchBar'
import { productApi } from '@/api'
import type { ProductSearchResponse } from '@/api/types/product'
import type { RootState } from '@/store'
import './index.scss'

export default function Search() {
  const router = useRouter()
  const [searchResults, setSearchResults] = useState<ProductSearchResponse[]>([])
  const [loading, setLoading] = useState(false)
  const [searchKeyword, setSearchKeyword] = useState('')
  const [hasSearched, setHasSearched] = useState(false)

  // 从store获取热门搜索
  const systemConfig = useSelector((state: RootState) => state.system.config)
  const hotSearch = (systemConfig && systemConfig.hot_search) || []

  useLoad(() => {
    // 获取路由参数中的搜索关键词
    const keyword = router.params.keyword
    if (keyword) {
      // 解码URL参数，处理中文字符
      const decodedKeyword = decodeURIComponent(keyword)
      setSearchKeyword(decodedKeyword)
      handleSearch(decodedKeyword)
    }
  })

  // 执行搜索
  const handleSearch = async (keyword: string) => {
    if (!keyword.trim()) return

    setLoading(true)
    setHasSearched(true)
    setSearchKeyword(keyword)

    try {
      const result = await productApi.searchProducts(keyword.trim())
      setSearchResults(result.list)
    } catch (error) {
      console.error('搜索失败:', error)
      setSearchResults([])
    } finally {
      setLoading(false)
    }
  }

  // 点击热门搜索标签
  const handleHotSearchClick = (keyword: string) => {
    handleSearch(keyword)
  }

  // 点击搜索结果项
  const handleResultClick = (product: ProductSearchResponse) => {
    navigateTo({
      url: `/pages/product-detail/index?id=${product.id}`
    })
  }

  return (
    <View className='search-page'>
      <View className='search-bar-wrapper'>
        <SearchBar
          placeholder='请输入您想找，想出售的东西'
          onSearch={handleSearch}
          showCustomerService={false}
          value={searchKeyword}
        />
      </View>

      <View className='content-area'>
        {/* 热门搜索 - 始终显示 */}
        {hotSearch.length > 0 && (
          <View className='hot-search-section'>
            <Text className='section-title'>热门搜索</Text>
            <View className='hot-search-tags'>
              {hotSearch.map((keyword, index) => (
                <View
                  key={index}
                  className='hot-tag'
                  onClick={() => handleHotSearchClick(keyword)}
                >
                  {keyword}
                </View>
              ))}
            </View>
          </View>
        )}

        {/* 搜索结果 */}
        {hasSearched && (
          <View className='search-results'>
            <View className='results-header'>
              <Text className='results-count'>
                结果：找到"{searchKeyword}"相关内容{searchResults.length}个
              </Text>
            </View>

            {loading ? (
              <View className='loading'>
                <Text>搜索中...</Text>
              </View>
            ) : searchResults.length > 0 ? (
              <View className='results-list'>
                {searchResults.map((product) => (
                  <View
                    key={product.id}
                    className='result-item'
                    onClick={() => handleResultClick(product)}
                  >
                    <Image
                      className='product-image'
                      src={product.image_url1}
                      mode='aspectFill'
                    />
                    <View className='product-info'>
                      <Text className='product-name'>{product.name}</Text>
                      <Text className='product-discount'>{product.discount_tip}</Text>
                    </View>
                    <View className='arrow-icon'>›</View>
                  </View>
                ))}
              </View>
            ) : (
              <View className='empty'>
                <Text className='empty-text'>暂无相关结果</Text>
                <Text className='empty-desc'>试试其他关键词吧</Text>
              </View>
            )}
          </View>
        )}
      </View>
    </View>
  )
}
