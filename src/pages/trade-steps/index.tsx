import { View, Text, Image } from '@tarojs/components'
import './index.scss'

export default function TradeSteps() {
  const steps = [
    {
      id: 1,
      title: '登录注册',
      icon: require('@/assets/images/step_icon1.png'),
      iconType: 'image',
      color: '#ff6b35',
      items: [
        '用手机号码注册账号',
        '用手机号获取验证码',
        '直接完成注册并登录'
      ]
    },
    {
      id: 2,
      title: '实名认证',
      icon: require('@/assets/images/step_icon2.png'),
      iconType: 'image',
      color: '#4ecdc4',
      items: [
        '输入真实姓名和身份证号码',
        '为了账户安全，实名认证后才可提现'
      ]
    },
    {
      id: 3,
      title: '提交卡密',
      icon: require('@/assets/images/step_icon3.png'),
      iconType: 'image',
      color: '#45b7d1',
      items: [
        '选择正确的面值',
        '提交相对应正确的卡号和卡密',
        '等待系统验证(恶意提交会被封号的)'
      ]
    },
    {
      id: 4,
      title: '账户提现',
      icon: require('@/assets/images/step_icon4.png'),
      iconType: 'image',
      color: '#f093fb',
      items: [
        '添加本人名下的提现账号',
        '进入提现页面提交提现申请',
        '资金秒到账，偶尔有延迟'
      ]
    }
  ]

  return (
    <View className='trade-steps-page'>
      <View className='steps-container'>
        {steps.map((step, index) => (
          <View key={step.id} className='step-item'>
            {/* 步骤头部 */}
            <View className='step-header'>
              {step.iconType === 'image' ? (
                <Image
                  className='step-icon-image'
                  src={step.icon}
                  mode='aspectFit'
                />
              ) : (
                <View className='step-icon' style={{ backgroundColor: step.color }}>
                  <Text className='icon-text'>{step.icon}</Text>
                </View>
              )}
              <Text className='step-title'>{step.title}</Text>
            </View>

            {/* 分割线 */}
            <View className='step-divider'></View>

            {/* 步骤内容 */}
            <View className='step-content'>
              {step.items.map((item, itemIndex) => (
                <View key={itemIndex} className='step-item-text'>
                  <Text className='step-number'>{itemIndex + 1}、</Text>
                  <Text className='step-description'>{item}</Text>
                </View>
              ))}
            </View>
          </View>
        ))}
      </View>
    </View>
  )
}
