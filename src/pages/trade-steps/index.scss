.trade-steps-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px 24px 48px 24px;
}

.steps-container {
  /* 步骤容器 */
}

.step-item {
  background: white;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.step-item:last-child {
  margin-bottom: 0;
}

/* 步骤头部 */
.step-header {
  display: flex;
  align-items: center;
  padding: 24px;
  background: white;
}

.step-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.icon-text {
  font-size: 24px;
  color: white;
}

.step-icon-image {
  width: 48px;
  height: 48px;
  margin-right: 20px;
  flex-shrink: 0;
}

.step-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  flex: 1;
}

/* 分割线 */
.step-divider {
  height: 1px;
  background: linear-gradient(
    to right,
    transparent 0%,
    #e0e0e0 20%,
    #e0e0e0 80%,
    transparent 100%
  );
  margin: 0 24px;
}

/* 步骤内容 */
.step-content {
  padding: 24px;
  background: #fafafa;
}

.step-item-text {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  line-height: 1.6;
}

.step-item-text:last-child {
  margin-bottom: 0;
}

.step-number {
  font-size: 24px;
  color: #666;
  margin-right: 6px;
  flex-shrink: 0;
  font-weight: 500;
}

.step-description {
  font-size: 24px;
  color: #666;
  line-height: 1.6;
  flex: 1;
}


