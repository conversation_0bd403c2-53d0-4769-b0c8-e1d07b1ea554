import { View, Text, Button } from '@tarojs/components'
import { useState, useEffect } from 'react'
import Taro, { useLoad, useDidShow } from '@tarojs/taro'
import { accountApi } from '../../api/account'
import type { AlipayAccountResponse, BankCardResponse } from '../../api/types/account'
import DeleteAccountModal from '../../components/DeleteAccountModal'
import './index.scss'

export default function WithdrawAccounts() {
  const [activeTab, setActiveTab] = useState<'alipay' | 'bank'>('alipay')
  const [alipayAccounts, setAlipayAccounts] = useState<AlipayAccountResponse[]>([])
  const [bankCards, setBankCards] = useState<BankCardResponse[]>([])
  const [loading, setLoading] = useState(false)

  // 删除弹窗相关状态
  const [deleteModalVisible, setDeleteModalVisible] = useState(false)
  const [deleteAccountType, setDeleteAccountType] = useState<'alipay' | 'bank'>('alipay')
  const [deleteAccountId, setDeleteAccountId] = useState<number>(0)

  useLoad(() => {
    console.log('WithdrawAccounts page loaded.')
    loadAccounts()
  })

  // 页面显示时刷新数据
  useDidShow(() => {
    console.log('WithdrawAccounts page show.')
    loadAccounts()
  })

  // 加载账户数据
  const loadAccounts = async () => {
    setLoading(true)
    try {
      const [alipayRes, bankRes] = await Promise.all([
        accountApi.getAllAlipayAccounts(),
        accountApi.getAllBankCards()
      ])
      setAlipayAccounts((alipayRes && alipayRes.list) || [])
      setBankCards((bankRes && bankRes.list) || [])
    } catch (error) {
      console.error('加载账户失败:', error)
      // 即使失败也设置为空数组，避免null值
      setAlipayAccounts([])
      setBankCards([])
      Taro.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 显示删除支付宝账户弹窗
  const handleDeleteAlipay = (account: AlipayAccountResponse) => {
    setDeleteAccountType('alipay')
    setDeleteAccountId(account.id)
    setDeleteModalVisible(true)
  }

  // 显示删除银行卡弹窗
  const handleDeleteBank = (card: BankCardResponse) => {
    setDeleteAccountType('bank')
    setDeleteAccountId(card.id)
    setDeleteModalVisible(true)
  }

  // 确认删除账户
  const handleConfirmDelete = async (tradePassword: string) => {
    try {
      Taro.showLoading({
        title: '删除中...'
      })

      if (deleteAccountType === 'alipay') {
        await accountApi.deleteAlipayAccount({
          id: deleteAccountId,
          trade_password: tradePassword
        })
      } else {
        await accountApi.deleteBankCard({
          id: deleteAccountId,
          trade_password: tradePassword
        })
      }

      Taro.hideLoading()
      setDeleteModalVisible(false)

      Taro.showToast({
        title: '删除成功',
        icon: 'success'
      })

      loadAccounts()
    } catch (error: any) {
      Taro.hideLoading()

      const errorMessage = (error && error.message) || '删除失败，请重试'
      Taro.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  }

  // 添加账户
  const handleAddAccount = () => {
    if (activeTab === 'alipay') {
      console.log('点击添加支付宝账户')
      Taro.navigateTo({
        url: '/pages/add-alipay/index'
      }).then(() => {
        console.log('跳转成功')
      }).catch((error) => {
        console.error('跳转失败:', error)
        Taro.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      })
    } else {
      console.log('点击添加银行卡账户')
      Taro.navigateTo({
        url: '/pages/add-bank-card/index'
      }).then(() => {
        console.log('跳转成功')
      }).catch((error) => {
        console.error('跳转失败:', error)
        Taro.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      })
    }
  }

  // 格式化账户号码（中间部分用*隐藏）
  const formatAccount = (account: string) => {
    if (account.length <= 8) return account
    const start = account.substring(0, 3)
    const end = account.substring(account.length - 4)
    return `${start}****${end}`
  }

  return (
    <View className='withdraw-accounts-page'>
      {/* 顶部标签 */}
      <View className='tab-header'>
        <View
          className={`tab-item ${activeTab === 'alipay' ? 'active' : ''}`}
          onClick={() => setActiveTab('alipay')}
        >
          <Text className='tab-text'>支付宝账户</Text>
          {activeTab === 'alipay' && <View className='tab-line'></View>}
        </View>
        <View
          className={`tab-item ${activeTab === 'bank' ? 'active' : ''}`}
          onClick={() => setActiveTab('bank')}
        >
          <Text className='tab-text'>银行卡账户</Text>
          {activeTab === 'bank' && <View className='tab-line'></View>}
        </View>
      </View>

      {/* 账户列表 */}
      <View className='account-list'>
        {activeTab === 'alipay' ? (
          // 支付宝账户列表
          alipayAccounts && alipayAccounts.length > 0 ? (
            alipayAccounts.map((account) => (
              <View key={account.id} className='account-item'>
                <View className='account-info'>
                  <View className='account-icon alipay-icon'>
                    <Text className='icon-text'>支</Text>
                  </View>
                  <View className='account-details'>
                    <Text className='account-name'>支付宝账户</Text>
                    <Text className='account-number'>{formatAccount(account.alipay_account)}</Text>
                  </View>
                </View>
                <Button
                  className='delete-btn'
                  onClick={() => handleDeleteAlipay(account)}
                >
                  删除账户
                </Button>
              </View>
            ))
          ) : (
            <View className='empty-state'>
              <Text className='empty-text'>暂无支付宝账户</Text>
            </View>
          )
        ) : (
          // 银行卡列表
          bankCards && bankCards.length > 0 ? (
            bankCards.map((card) => (
              <View key={card.id} className='account-item'>
                <View className='account-info'>
                  <View className='account-icon bank-icon'>
                    <Text className='icon-text'>银</Text>
                  </View>
                  <View className='account-details'>
                    <Text className='account-name'>{card.bank_name}</Text>
                    <Text className='account-number'>{formatAccount(card.bank_account)}</Text>
                  </View>
                </View>
                <Button
                  className='delete-btn'
                  onClick={() => handleDeleteBank(card)}
                >
                  删除账户
                </Button>
              </View>
            ))
          ) : (
            <View className='empty-state'>
              <Text className='empty-text'>暂无银行卡</Text>
            </View>
          )
        )}
      </View>

      {/* 底部添加按钮 */}
      <View className='bottom-section'>
        <Button
          className='add-button'
          onTap={handleAddAccount}
        >
          {activeTab === 'alipay' ? '添加支付宝账户' : '添加银行卡账户'}
        </Button>
      </View>

      {/* 删除账户弹窗 */}
      <DeleteAccountModal
        visible={deleteModalVisible}
        accountType={deleteAccountType}
        onClose={() => setDeleteModalVisible(false)}
        onConfirm={handleConfirmDelete}
      />
    </View>
  )
}
