.withdraw-accounts-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 顶部标签 */
.tab-header {
  background: white;
  display: flex;
  padding: 0 32px;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 0 24px;
  position: relative;
  cursor: pointer;
}

.tab-text {
  font-size: 28px;
  color: #666;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #1890ff;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #1890ff;
  border-radius: 2px;
}

/* 账户列表 */
.account-list {
  flex: 1;
  padding: 24px 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.account-item {
  background: white;
  border-radius: 16px;
  padding: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.account-info {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
}

.account-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.alipay-icon {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.bank-icon {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.icon-text {
  font-size: 32px;
  font-weight: 600;
  color: white;
}

.account-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.account-name {
  font-size: 28px;
  font-weight: 500;
  color: #333;
}

.account-number {
  font-size: 24px;
  color: #666;
}

.delete-btn {
  padding: 12px 24px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 32px;
  font-size: 22px;
  color: #666;
  min-width: 120px;

  &::after {
    border: none;
  }

  &:active {
    background: #e6e6e6;
  }
}

/* 底部添加按钮 */
.bottom-section {
  background: white;
  padding: 32px;
  border-top: 1px solid #f0f0f0;
}

.add-button {
  width: 100%;
  height: 88px;
  background: #1890ff;
  border: none;
  border-radius: 44px;
  font-size: 28px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);
    background: #096dd9;
  }

  &::after {
    border: none;
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 32px;
}

.empty-text {
  font-size: 28px;
  color: #999;
}
