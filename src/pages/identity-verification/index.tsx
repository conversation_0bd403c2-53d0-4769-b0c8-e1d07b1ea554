import { View, Text, Image } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import { useSelector } from 'react-redux'
import Taro from '@tarojs/taro'
import type { RootState } from '../../store'
import { AuthType } from '../../api/types/user'
import './index.scss'

export default function IdentityVerification() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo)

  useLoad(() => {
    console.log('IdentityVerification page loaded.')
  })

  // 获取认证状态图片
  const getAuthImage = (authType: AuthType, targetType: AuthType) => {
    if (authType >= targetType) {
      return require('@/assets/images/step_1.png') // 已认证图片
    } else {
      return require('@/assets/images/step_0.png') // 未认证图片
    }
  }

  // 处理认证点击
  const handleAuthClick = (authType: AuthType) => {
    const currentAuthType = (userInfo && userInfo.auth_type) || AuthType.None

    if (currentAuthType >= authType) {
      Taro.showToast({
        title: '您已完成此认证',
        icon: 'none'
      })
      return
    }

    // 检查是否需要先完成初级认证
    if (authType === AuthType.Advanced && currentAuthType < AuthType.Basic) {
      Taro.showToast({
        title: '请先完成初级认证',
        icon: 'none'
      })
      return
    }

    // 跳转到对应的认证页面
    const authPages = {
      [AuthType.Basic]: '/pages/basic-auth/index',
      [AuthType.Advanced]: '/pages/advanced-auth/index'
    }

    const targetPage = authPages[authType]
    Taro.navigateTo({
      url: targetPage
    })
  }

  const currentAuthType = (userInfo && userInfo.auth_type) || AuthType.None

  return (
    <View className='identity-verification-page'>
      {/* 初级认证 */}
      <View
        className='auth-item'
        onClick={() => handleAuthClick(AuthType.Basic)}
      >
        <View className='auth-content'>
          <View className='auth-header'>
            <View className='auth-left-icon'>
              <Image
                className='left-icon-image'
                src={require('@/assets/images/step_icon1.png')}
                mode='aspectFit'
              />
            </View>
            <Text className='auth-title'>初级认证</Text>
            <View className='auth-icon'>
              <Image
                className='auth-image'
                src={getAuthImage(currentAuthType, AuthType.Basic)}
                mode='aspectFit'
              />
            </View>
          </View>

          <View className='auth-description-row'>
            <View className='auth-spacer'></View>
            <Text className='auth-description'>
              完成初级认证后，系统可用额度为2000元，平台大部分产品均可使用。
            </Text>
          </View>
        </View>
      </View>

      {/* 高级认证 */}
      <View
        className='auth-item'
        onClick={() => handleAuthClick(AuthType.Advanced)}
      >
        <View className='auth-content'>
          <View className='auth-header'>
            <View className='auth-left-icon'>
              <Image
                className='left-icon-image'
                src={require('@/assets/images/step_icon2.png')}
                mode='aspectFit'
              />
            </View>
            <Text className='auth-title'>高级认证</Text>
            <View className='auth-icon'>
              <Image
                className='auth-image'
                src={getAuthImage(currentAuthType, AuthType.Advanced)}
                mode='aspectFit'
              />
            </View>
          </View>

          <View className='auth-description-row'>
            <View className='auth-spacer'></View>
            <Text className='auth-description'>
              完成高级认证后，系统可用额度不限额，平台所有产品均可使用。
            </Text>
          </View>
        </View>
      </View>
    </View>
  )
}
