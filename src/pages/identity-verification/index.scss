.identity-verification-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
}

.auth-item {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  display: flex;
  align-items: flex-start;
  gap: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  &:active {
    transform: translateY(1px);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

/* 左侧图标 */
.auth-left-icon {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.left-icon-image {
  width: 64px;
  height: 64px;
}

/* 右侧认证状态图标 */
.auth-icon {
  width: 100px !important;
  height: 100px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.auth-image {
  width: 100px !important;
  height: 100px !important;
}

.auth-content {
  flex: 1;
  min-width: 0;
}

.auth-header {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 0;
}

.auth-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.auth-description-row {
  display: flex;
  gap: 16px;
}

.auth-spacer {
  width: 64px; /* 与左侧图标宽度相同 */
  flex-shrink: 0;
}

.auth-description {
  font-size: 24px;
  color: #666;
  line-height: 1.6;
  margin: 0;
  flex: 1;
}

/* 响应式调整 */

