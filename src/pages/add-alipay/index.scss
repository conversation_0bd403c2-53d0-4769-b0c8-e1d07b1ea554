.add-alipay-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 表单区域 */
.form-section {
  background: white;
  margin: 24px 32px 0;
  border-radius: 12px;
  overflow: hidden;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 36px 32px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.form-label {
  font-size: 28px;
  color: #999;
  font-weight: 400;
  margin-right: 32px;
  min-width: 140px;
  flex-shrink: 0;
}

.form-value {
  flex: 1;
  font-size: 28px;
  color: #333;
  font-weight: 500;
  text-align: right;
}

.form-input {
  flex: 1;
  font-size: 28px;
  color: #333;
  text-align: right;
  background: transparent;
  border: none;
  outline: none;

  &::placeholder {
    color: #ccc;
    font-size: 28px;
  }
}

/* 操作提示 */
.tips-section {
  margin: 40px 32px 0;
  padding: 0;
}

.tips-title {
  font-size: 28px;
  color: #999;
  font-weight: 500;
  display: block;
  margin-bottom: 16px;
}

.tips-text {
  font-size: 24px;
  color: #999;
  line-height: 1.6;
  display: block;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

/* 底部按钮区域 */
.bottom-section {
  margin-top: auto;
  padding: 40px 32px 32px;
}

.save-button {
  width: 100%;
  height: 88px;
  background: #1890ff;
  border: none;
  border-radius: 44px;
  font-size: 28px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);
    background: #096dd9;
  }

  &::after {
    border: none;
  }
}
