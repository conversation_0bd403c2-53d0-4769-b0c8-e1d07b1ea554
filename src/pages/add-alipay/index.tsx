import { View, Text, Input, Button } from '@tarojs/components'
import { useState } from 'react'
import { useSelector } from 'react-redux'
import Taro, { useLoad } from '@tarojs/taro'
import { accountApi } from '../../api/account'
import type { RootState } from '../../store'
import './index.scss'

export default function AddAlipay() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo)
  const [alipayAccount, setAlipayAccount] = useState('')
  const [confirmAccount, setConfirmAccount] = useState('')

  useLoad(() => {
    console.log('AddAlipay page loaded.')
  })

  // 处理保存账号
  const handleSave = async () => {
    if (!alipayAccount.trim()) {
      Taro.showToast({
        title: '请输入支付宝账号',
        icon: 'none'
      })
      return
    }

    if (!confirmAccount.trim()) {
      Taro.showToast({
        title: '请确认支付宝账号',
        icon: 'none'
      })
      return
    }

    if (alipayAccount.trim() !== confirmAccount.trim()) {
      Taro.showToast({
        title: '两次输入的账号不一致',
        icon: 'none'
      })
      return
    }

    try {
      Taro.showLoading({
        title: '保存中...'
      })

      await accountApi.addAlipayAccount({
        alipay_account: alipayAccount.trim()
      })

      Taro.hideLoading()

      Taro.showToast({
        title: '保存成功',
        icon: 'success'
      })

      // 延迟返回上一页，并通知刷新
      setTimeout(() => {
        Taro.navigateBack({
          success: () => {
            // 通过事件总线通知提现账户页面刷新
            Taro.eventCenter.trigger('refreshAccounts')
          }
        })
      }, 1500)

    } catch (error: any) {
      Taro.hideLoading()

      const errorMessage = (error && error.message) || '保存失败，请重试'
      Taro.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  }

  return (
    <View className='add-alipay-page'>
      {/* 表单区域 */}
      <View className='form-section'>
        {/* 账号类型 */}
        <View className='form-item'>
          <Text className='form-label'>账号类型</Text>
          <Text className='form-value'>支付宝</Text>
        </View>

        {/* 账号姓名 */}
        <View className='form-item'>
          <Text className='form-label'>账号姓名</Text>
          <Text className='form-value'>{(userInfo && userInfo.real_name) || '未设置'}</Text>
        </View>

        {/* 账户账号 */}
        <View className='form-item'>
          <Text className='form-label'>账户账号</Text>
          <Input
            className='form-input'
            placeholder='请输入支付宝账号'
            value={alipayAccount}
            onInput={(e) => setAlipayAccount(e.detail.value)}
          />
        </View>

        {/* 确认账号 */}
        <View className='form-item'>
          <Text className='form-label'>确认账号</Text>
          <Input
            className='form-input'
            placeholder='请再次输入个人的账号'
            value={confirmAccount}
            onInput={(e) => setConfirmAccount(e.detail.value)}
          />
        </View>
      </View>

      {/* 操作提示 */}
      <View className='tips-section'>
        <Text className='tips-title'>操作提示：</Text>
        <Text className='tips-text'>1、请仔细核对支付宝账号是否正确。</Text>
        <Text className='tips-text'>2、请填写本人支付宝账号，支付宝账号和实名信息必须一致，否则无法提现。</Text>
      </View>

      {/* 底部保存按钮 */}
      <View className='bottom-section'>
        <Button
          className='save-button'
          onClick={handleSave}
        >
          保存账号
        </Button>
      </View>
    </View>
  )
}
