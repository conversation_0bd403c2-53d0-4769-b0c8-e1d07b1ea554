.basic-auth-page {
  min-height: 78vh;
  background-color: #f5f5f5;
  padding: 0 0 180px 0; /* 底部留出空间给固定按钮 */
  display: flex;
  flex-direction: column;
}

/* 顶部提示横幅 */
.notice-banner {
  background: linear-gradient(90deg, #fff2e6 0%, #ffecd9 100%);
  padding: 24px 32px;
  border-bottom: 1px solid #f0f0f0;
}

.notice-text {
  font-size: 24px;
  color: #d4380d;
  line-height: 1.5;
  text-align: left;
}

/* 表单区域 */
.form-section {
  background: white;
  margin-top: 24px;
  padding: 0 32px 40px;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 32px 0 40px;
  gap: 16px;
}

.header-line {
  width: 6px;
  height: 32px;
  background: #1890ff;
  border-radius: 3px;
  flex-shrink: 0;
}

.section-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 48px;
}

.input-item {
  display: flex;
  align-items: center;
  gap: 24px;
}

.input-label {
  font-size: 28px;
  color: #333;
  font-weight: 500;
  min-width: 160px;
  flex-shrink: 0;
}

.input-field {
  flex: 1;
  font-size: 28px;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  padding: 16px 0;
  border-bottom: 2px solid #f0f0f0;
  transition: border-color 0.3s ease;

  &::placeholder {
    color: #bfbfbf;
    font-size: 28px;
  }

  &:focus {
    border-bottom-color: #1890ff;
  }
}

/* 底部信息 */
.bottom-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  padding: 0 32px;
  padding-top: 0;
  gap: 16px;
}

.info-text {
  font-size: 28px;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

/* 底部区域 */
.bottom-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 32px;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* 隐私政策区域 */
.privacy-section {
  padding: 0 0 32px 0;
}

.checkbox-row {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  cursor: pointer;
}

.custom-checkbox {
  width: 36px;
  height: 36px;
  border: 2px solid #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4px;
  transition: all 0.3s ease;

  &.checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
  }
}

.checkmark {
  color: white;
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
}

.privacy-text {
  font-size: 24px;
  color: #666;
  line-height: 1.6;
  flex: 1;
}

.privacy-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

/* 提交按钮 */
.submit-section {
  padding: 0;
}

.submit-button {
  width: 100%;
  height: 88px;
  background: var(--primary-color);
  border: none;
  border-radius: 44px;
  font-size: 28px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(18, 150, 219, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(18, 150, 219, 0.4);
    background: var(--primary-hover);
  }

  &::after {
    border: none;
  }
}


