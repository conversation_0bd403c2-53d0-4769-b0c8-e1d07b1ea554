import { View, Text, Input, Button } from '@tarojs/components'
import { useState } from 'react'
import { useDispatch } from 'react-redux'
import Taro, { useLoad } from '@tarojs/taro'
import { userApi } from '../../api/user'
import { fetchCurrentUserInfo } from '../../store/user'
import './index.scss'

export default function BasicAuth() {
  const dispatch = useDispatch()
  const [realName, setRealName] = useState('')
  const [idCard, setIdCard] = useState('')
  const [agreed, setAgreed] = useState(false)

  useLoad(() => {
    console.log('BasicAuth page loaded.')
  })

  // 处理提交认证
  const handleSubmit = async () => {
    if (!realName.trim()) {
      Taro.showToast({
        title: '请输入真实姓名',
        icon: 'none'
      })
      return
    }

    if (!idCard.trim()) {
      Taro.showToast({
        title: '请输入身份证号',
        icon: 'none'
      })
      return
    }

    // 简单的身份证号格式验证
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
    if (!idCardRegex.test(idCard)) {
      Taro.showToast({
        title: '请输入正确的身份证号',
        icon: 'none'
      })
      return
    }

    if (!agreed) {
      console.log('Agreed status:', agreed)
      Taro.showToast({
        title: '请先阅读并同意隐私政策',
        icon: 'none'
      })
      return
    }

    try {
      // 显示加载中
      Taro.showLoading({
        title: '提交中...'
      })

      // 调用实名认证API
      await userApi.realNameAuth({
        real_name: realName.trim(),
        id_card: idCard.trim()
      })

      Taro.hideLoading()

      // 刷新用户信息
      try {
        await dispatch(fetchCurrentUserInfo() as any)
      } catch (error) {
        console.log('刷新用户信息失败:', error)
      }

      Taro.showToast({
        title: '认证成功',
        icon: 'success'
      })

      // 延迟返回上一页
      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)

    } catch (error: any) {
      Taro.hideLoading()

      const errorMessage = (error && error.message) || '认证失败，请重试'
      Taro.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  }

  // 处理隐私政策点击
  const handlePrivacyClick = () => {
    Taro.navigateTo({
      url: '/pages/privacy-policy/index'
    })
  }

  return (
    <View className='basic-auth-page'>
      {/* 顶部提示 */}
      <View className='notice-banner'>
        <Text className='notice-text'>
          余额提现也必须是这个身份证开户的微信、支付宝，请注意填写
        </Text>
      </View>

      {/* 基本信息 */}
      <View className='form-section'>
        <View className='section-header'>
          <View className='header-line'></View>
          <Text className='section-title'>基本信息</Text>
        </View>

        <View className='form-content'>
          {/* 真实姓名 */}
          <View className='input-item'>
            <Text className='input-label'>真实姓名：</Text>
            <Input
              className='input-field'
              placeholder='请输入您的真实姓名'
              value={realName}
              onInput={(e) => setRealName(e.detail.value)}
              maxlength={20}
            />
          </View>

          {/* 身份证号 */}
          <View className='input-item'>
            <Text className='input-label'>身份证号：</Text>
            <Input
              className='input-field'
              placeholder='请输入您的身份证号'
              value={idCard}
              onInput={(e) => setIdCard(e.detail.value)}
              maxlength={18}
            />
          </View>
        </View>
      </View>

      {/* 底部说明 */}
      <View className='bottom-info'>
        <Text className='info-text'>
          您提供的信息仅作为京海回收实名认证使用
        </Text>
        <Text className='info-text'>
          我们不会泄露用户任何隐私
        </Text>
      </View>

      {/* 隐私政策同意和提交按钮 */}
      <View className='bottom-section'>
        {/* 隐私政策同意 */}
        <View className='privacy-section'>
          <View className='checkbox-row' onClick={() => setAgreed(!agreed)}>
            <View className={`custom-checkbox ${agreed ? 'checked' : ''}`}>
              {agreed && <Text className='checkmark'>✓</Text>}
            </View>
            <Text className='privacy-text'>
              我已阅读、理解并接受
              <Text
                className='privacy-link'
                onClick={(e) => {
                  e.stopPropagation()
                  handlePrivacyClick()
                }}
              >
                【隐私政策】
              </Text>
              ，授权京海回收及第三方服务者提供身份信息、面部信息用于身份认证
            </Text>
          </View>
        </View>

        {/* 提交按钮 */}
        <View className='submit-section'>
          <Button
            className='submit-button'
            onClick={handleSubmit}
          >
            提交认证
          </Button>
        </View>
      </View>
    </View>
  )
}
