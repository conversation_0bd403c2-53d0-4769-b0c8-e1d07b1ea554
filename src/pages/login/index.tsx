import { View, Text, Input, Button, Checkbox } from '@tarojs/components'
import { useState } from 'react'
import Taro from '@tarojs/taro'
import { useDispatch } from 'react-redux'
import { authApi } from '../../api/auth'
import request from '../../utils/request'
import { fetchCurrentUserInfo } from '../../store/user'
import './index.scss'

export default function Login() {
  const dispatch = useDispatch()
  const [phone, setPhone] = useState('')
  const [code, setCode] = useState('')
  const [agreed, setAgreed] = useState(false)
  const [countdown, setCountdown] = useState(0)

  // 获取验证码
  const getVerificationCode = async () => {
    if (!phone) {
      Taro.showToast({
        title: '请输入手机号码',
        icon: 'none'
      })
      return
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      Taro.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      })
      return
    }

    try {
      // 调用发送验证码API
      await authApi.sendSMSCode({ phone })

      Taro.showToast({
        title: '验证码已发送',
        icon: 'success'
      })

      // 开始倒计时
      setCountdown(60)
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } catch (error) {
      console.error('发送验证码失败:', error)
      Taro.showToast({
        title: '发送验证码失败，请重试',
        icon: 'none'
      })
    }
  }

  // 登录
  const handleLogin = async () => {
    if (!phone) {
      Taro.showToast({
        title: '请输入手机号码',
        icon: 'none'
      })
      return
    }

    if (!code) {
      Taro.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return
    }

    if (!/^\d{6}$/.test(code)) {
      Taro.showToast({
        title: '请输入6位数字验证码',
        icon: 'none'
      })
      return
    }

    if (!agreed) {
      Taro.showToast({
        title: '请先同意用户协议和隐私协议',
        icon: 'none'
      })
      return
    }

    try {
      // 显示加载状态
      Taro.showLoading({
        title: '登录中...'
      })

      // 调用登录API
      const response = await authApi.loginWithCode({ phone, code })

      // 保存token到小程序缓存中
      request.setAuthToken(response.token)

      // 获取用户信息并持久化到store
      try {
        await dispatch(fetchCurrentUserInfo() as any)
      } catch (userInfoError) {
        console.error('获取用户信息失败:', userInfoError)
        // 即使获取用户信息失败，也不影响登录流程
      }

      Taro.hideLoading()

      Taro.showToast({
        title: '登录成功',
        icon: 'success'
      })

      // 登录成功后跳转
      setTimeout(() => {
        Taro.switchTab({
          url: '/pages/index/index'
        })
      }, 1500)
    } catch (error) {
      Taro.hideLoading()
      console.error('登录失败:', error)
      Taro.showToast({
        title: '登录失败，请检查验证码',
        icon: 'none'
      })
    }
  }

  // 暂不登录
  const skipLogin = () => {
    Taro.switchTab({
      url: '/pages/index/index'
    })
  }

  // 跳转到用户协议页面
  const goToUserAgreement = () => {
    Taro.navigateTo({
      url: '/pages/user-agreement/index'
    })
  }

  // 跳转到隐私政策页面
  const goToPrivacyPolicy = () => {
    Taro.navigateTo({
      url: '/pages/privacy-policy/index'
    })
  }

  return (
    <View className='login-container'>

      {/* 背景装饰 */}
      <View className='bg-decoration'>
        <View className='circle circle-1'></View>
        <View className='circle circle-2'></View>
        <View className='planet'></View>
      </View>

      {/* 欢迎文字 */}
      <View className='welcome-text'>
        <Text className='greeting'>你好，</Text>
        <Text className='subtitle'>欢迎登录京海回收</Text>
      </View>

      {/* 登录表单 */}
      <View className='login-form'>
        <View className='form-title'>
          <Text>手机号码验证登录</Text>
        </View>

        {/* 手机号输入 */}
        <View className='input-group'>
          <View className='input-wrapper'>
            <View className='input-icon phone-icon'></View>
            <Input
              className='input-field'
              placeholder='请输入手机号码'
              type='number'
              maxlength={11}
              value={phone}
              onInput={(e) => setPhone(e.detail.value)}
            />
          </View>
        </View>

        {/* 验证码输入 */}
        <View className='input-group'>
          <View className='input-wrapper'>
            <View className='input-icon code-icon'></View>
            <Input
              className='input-field'
              placeholder='请输入验证码'
              type='number'
              maxlength={6}
              value={code}
              onInput={(e) => setCode(e.detail.value)}
            />
            <View className='get-code-btn' onClick={getVerificationCode}>
              <Text className={countdown > 0 ? 'disabled' : ''}>
                {countdown > 0 ? `${countdown}s` : '获取验证码?'}
              </Text>
            </View>
          </View>
        </View>

        {/* 登录按钮 */}
        <Button className='login-btn' onClick={handleLogin}>
          登录账户
        </Button>

        {/* 暂不登录 */}
        <View className='skip-login' onClick={skipLogin}>
          <Text>暂不登录</Text>
        </View>

        {/* 协议同意 */}
        <View className='agreement'>
          <View className='checkbox-wrapper' onClick={() => setAgreed(!agreed)}>
            <Checkbox
              className='checkbox'
              value='agreed'
              checked={agreed}
            />
          </View>
          <Text className='agreement-text'>
              已阅读并同意
            <Text className='link' onClick={goToUserAgreement}>京海用户协议</Text>和
            <Text className='link' onClick={goToPrivacyPolicy}>隐私协议</Text>
          </Text>
        </View>
      </View>
    </View>
  )
}
