import { View, Text, Image } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import { useSelector } from 'react-redux'
import Taro from '@tarojs/taro'
import type { RootState } from '../../store'
import userIcon1 from '../../assets/images/user_icon1.png'
import userIcon2 from '../../assets/images/user_icon2.png'
import userIcon3 from '../../assets/images/user_icon3.png'
import userIcon4 from '../../assets/images/user_icon4.png'
import userIcon5 from '../../assets/images/user_icon5.png'
import userIcon6 from '../../assets/images/user_icon6.png'
import userIcon7 from '../../assets/images/user_icon7.png'
import member0 from '../../assets/images/member_0.png'
import member1 from '../../assets/images/member_1.png'
import member2 from '../../assets/images/member_2.png'
import './index.scss'

export default function User() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo)

  useLoad(() => {
    console.log('User page loaded.')
  })

  // 跳转到登录页面
  const navigateToLogin = () => {
    Taro.navigateTo({
      url: '/pages/login/index'
    })
  }

  // 处理菜单项点击
  const handleMenuClick = (item: any) => {
    // 帮助与客服不需要登录，直接跳转
    if (item.icon === 'service') {
      Taro.navigateTo({
        url: '/pages/help/index'
      })
      return
    }

    // 其他功能需要登录
    if (!userInfo) {
      // 未登录用户点击功能菜单，提示登录
      Taro.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        navigateToLogin()
      }, 1500)
      return
    }

    // 已登录用户的功能处理
    switch (item.icon) {
      case 'record':
        Taro.switchTab({
          url: '/pages/order/index'
        })
        break
      case 'withdraw':
        Taro.showToast({ title: '提现记录功能开发中', icon: 'none' })
        break
      case 'settings':
        Taro.navigateTo({
          url: '/pages/settings/index'
        })
        break
      case 'invite':
        Taro.showToast({ title: '好友邀请功能开发中', icon: 'none' })
        break
      case 'notification':
        Taro.navigateTo({
          url: '/pages/notifications/index'
        })
        break
      case 'about':
        Taro.showToast({ title: '关于我们功能开发中', icon: 'none' })
        break
      default:
        break
    }
  }

  // 处理提现按钮点击
  const handleWithdraw = () => {
    if (!userInfo) {
      Taro.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        navigateToLogin()
      }, 1500)
      return
    }
    Taro.showToast({ title: '提现功能开发中', icon: 'none' })
  }

  // 第一组功能菜单项
  const menuItems1 = [
    {
      icon: 'record',
      title: '出售记录',
      arrow: true
    },
    {
      icon: 'withdraw',
      title: '提现记录',
      arrow: true
    },
    {
      icon: 'settings',
      title: '账号设置',
      arrow: true
    },
    {
      icon: 'invite',
      title: '好友邀请',
      arrow: true,
      subtitle: '邀请好友有惊喜'
    }
  ]

  // 第二组功能菜单项
  const menuItems2 = [
    {
      icon: 'notification',
      title: '消息通知',
      arrow: true
    },
    {
      icon: 'service',
      title: '帮助与客服',
      arrow: true
    },
    {
      icon: 'about',
      title: '关于我们',
      arrow: true
    }
  ]

  // 渲染会员标识
  const renderMemberBadge = () => {
    if (!userInfo) return null

    console.log('用户类型:', userInfo.user_type)

    // 根据用户类型显示对应的会员标识
    switch (userInfo.user_type) {
      case 1: // 普通用户
        return (
          <Image
            className='member-badge'
            src={member0}
            mode='aspectFit'
          />
        )
      case 2: // 中级会员
        return (
          <Image
            className='member-badge'
            src={member1}
            mode='aspectFit'
          />
        )
      case 3: // 高级会员
        return (
          <Image
            className='member-badge'
            src={member2}
            mode='aspectFit'
          />
        )
      default:
        // 默认显示普通用户标识
        return (
          <Image
            className='member-badge'
            src={member0}
            mode='aspectFit'
          />
        )
    }
  }

  // 渲染图标
  const renderIcon = (iconType: string) => {
    const iconStyle = 'menu-icon-svg'
    switch (iconType) {
      case 'record':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon1}
              mode='aspectFit'
            />
          </View>
        )
      case 'withdraw':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon2}
              mode='aspectFit'
            />
          </View>
        )
      case 'settings':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon3}
              mode='aspectFit'
            />
          </View>
        )
      case 'invite':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon4}
              mode='aspectFit'
            />
          </View>
        )
      case 'notification':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon5}
              mode='aspectFit'
            />
          </View>
        )
      case 'service':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon6}
              mode='aspectFit'
            />
          </View>
        )
      case 'about':
        return (
          <View className={iconStyle}>
            <Image
              className='menu-icon-image'
              src={userIcon7}
              mode='aspectFit'
            />
          </View>
        )
      default:
        return <View className={iconStyle}></View>
    }
  }

  return (
    <View className='user-page'>
      {/* 顶部用户信息区域 */}
      <View className='user-header'>
        <View className='user-info' onClick={userInfo ? undefined : navigateToLogin}>
          <View className='avatar-container'>
            {(userInfo && userInfo.avatar) ? (
              <Image
                className='avatar'
                src={userInfo.avatar}
                mode='aspectFill'
              />
            ) : (
              <View className='avatar avatar-placeholder'>
                <View className='avatar-icon'></View>
              </View>
            )}
          </View>
          <View className='user-details'>
            {userInfo ? (
              <>
                <Text className='username'>{userInfo.username || '用户'}</Text>
                <Text className='phone'>京海专业的卡券寄售平台~~</Text>
              </>
            ) : (
              <View className='login-prompt'>
                <Text className='login-text'>请先进行登录/注册</Text>
                <Text className='login-desc'>京海专业的卡券寄售平台~~</Text>
              </View>
            )}
          </View>
          {userInfo ? renderMemberBadge() : <View className='arrow-right'>›</View>}
        </View>
      </View>

      {/* 账户余额卡片 */}
      <View className='balance-card'>
        <View className='balance-content'>
          <Text className='balance-label'>账户余额</Text>
          <Text className='balance-amount'>
            {userInfo ? `${(userInfo.balance || 0).toFixed(2)}` : '--'} 元
          </Text>
        </View>
        <View className='withdraw-btn' onClick={handleWithdraw}>
          <Text className='withdraw-text'>提现</Text>
        </View>
      </View>

      {/* 统计数据区域 */}
      <View className='stats-container'>
        <View className='stat-item'>
          <Text className='stat-label'>推广效益</Text>
          <Text className='stat-value'>
            {userInfo ? `${(userInfo.promotion_earning || 0).toFixed(2)}` : '--'}
          </Text>
        </View>
        <View className='stat-item'>
          <Text className='stat-label'>我的好友</Text>
          <Text className='stat-value'>
            {userInfo ? `${userInfo.friend_count || 0}` : '--'}
          </Text>
        </View>
      </View>

      {/* 第一组功能菜单列表 */}
      <View className='menu-list'>
        {menuItems1.map((item, index) => (
          <View key={index} className='menu-item' onClick={() => handleMenuClick(item)}>
            <View className='menu-left'>
              {renderIcon(item.icon)}
              <Text className='menu-title'>{item.title}</Text>
            </View>
            <View className='menu-right'>
              {item.subtitle && <Text className='menu-subtitle'>{item.subtitle}</Text>}
              {item.arrow && <View className='menu-arrow'>›</View>}
            </View>
          </View>
        ))}
      </View>

      {/* 第二组功能菜单列表 */}
      <View className='menu-list menu-list-second'>
        {menuItems2.map((item, index) => (
          <View key={index} className='menu-item' onClick={() => handleMenuClick(item)}>
            <View className='menu-left'>
              {renderIcon(item.icon)}
              <Text className='menu-title'>{item.title}</Text>
            </View>
            {item.arrow && <View className='menu-arrow'>›</View>}
          </View>
        ))}
      </View>
    </View>
  )
}
