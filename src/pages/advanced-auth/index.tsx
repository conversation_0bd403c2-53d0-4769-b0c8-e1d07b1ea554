import { View, Text, Button } from '@tarojs/components'
import { useState } from 'react'
import { useSelector } from 'react-redux'
import Taro, { useLoad } from '@tarojs/taro'
import type { RootState } from '../../store'
import { AuthType } from '../../api/types/user'
import AdvancedAuthModal from '../../components/AdvancedAuthModal'
import './index.scss'

export default function AdvancedAuth() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo)
  const [modalVisible, setModalVisible] = useState(false)

  useLoad(() => {
    console.log('AdvancedAuth page loaded.')
  })

  // 处理一键腾讯云慧眼认证
  const handleAdvancedAuth = () => {
    // 检查是否已完成初级认证
    if (!userInfo || userInfo.auth_type < AuthType.Basic) {
      Taro.showToast({
        title: '请先完成初级认证',
        icon: 'none'
      })
      return
    }

    // 检查是否已完成高级认证
    if (userInfo.auth_type >= AuthType.Advanced) {
      Taro.showToast({
        title: '您已完成高级认证',
        icon: 'none'
      })
      return
    }

    // 显示认证弹窗
    setModalVisible(true)
  }

  // 关闭弹窗
  const handleCloseModal = () => {
    setModalVisible(false)
  }

  // 格式化身份证号（中间部分用*号隐藏）
  const formatIdCard = (idCard: string) => {
    if (!idCard || idCard.length < 18) return idCard
    return idCard.substring(0, 6) + '****' + idCard.substring(14)
  }

  return (
    <View className='advanced-auth-page'>
      {/* 顶部提示 */}
      <View className='notice-banner'>
        <Text className='notice-text'>
          余额提现也必须是这个身份证所开的微信、支付宝，请注意填写
        </Text>
      </View>

      {/* 基本信息 */}
      <View className='form-section'>
        <View className='section-header'>
          <View className='header-line'></View>
          <Text className='section-title'>基本信息</Text>
        </View>

        <View className='form-content'>
          {/* 真实姓名 */}
          <View className='info-item'>
            <Text className='info-label'>真实姓名：</Text>
            <Text className='info-value'>
              {(userInfo && userInfo.real_name) || '未设置'}
            </Text>
          </View>

          {/* 身份证号 */}
          <View className='info-item'>
            <Text className='info-label'>身份证号：</Text>
            <Text className='info-value'>
              {(userInfo && userInfo.id_card) ? formatIdCard(userInfo.id_card) : '未设置'}
            </Text>
          </View>
        </View>
      </View>

      {/* 底部说明 */}
      <View className='bottom-info'>
        <Text className='info-text'>
          您提供的信息仅作为京海回收实名认证使用
        </Text>
        <Text className='info-text'>
          我们不会泄露用户任何隐私
        </Text>
      </View>

      {/* 提交按钮 */}
      <View className='bottom-section'>
        <View className='submit-section'>
          <Button
            className='submit-button'
            onClick={handleAdvancedAuth}
          >
            一键腾讯云慧眼认证
          </Button>
        </View>
      </View>

      {/* 高级认证弹窗 */}
      <AdvancedAuthModal
        visible={modalVisible}
        onClose={handleCloseModal}
      />
    </View>
  )
}
