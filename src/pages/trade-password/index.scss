.trade-password-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
  display: flex;
  flex-direction: column;
}

/* 输入区域 */
.input-section {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 24px;
}

/* 交易密码输入区域 */
.password-section {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: auto;
}

.input-item {
  display: flex;
  align-items: center;
  padding: 36px 32px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.input-label {
  font-size: 34px;
  color: #333;
  font-weight: 500;
  margin-right: 32px;
  min-width: 200px;
}

.input-field {
  flex: 1;
  font-size: 34px;
  color: #333;
  text-align: right;

  &::placeholder {
    color: #999;
    font-size: 30px;
  }
}

.input-value {
  flex: 1;
  font-size: 34px;
  color: #999;
  text-align: right;
}

.input-field-full {
  width: 100%;
  font-size: 34px;
  color: #333;
  text-align: left;
  flex: 1;

  &::placeholder {
    color: #999;
    font-size: 30px;
  }
}

/* 只有输入框的项目样式 */
.input-only {
  .input-right {
    width: 100%;
  }
}

/* 验证码输入框特殊样式 */
.verify-code-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;

  .input-field-full {
    flex: 1;
    min-width: 0; /* 防止输入框溢出 */
  }
}

/* 验证码输入特殊布局 */
.input-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
}

.get-code-btn {
  font-size: 28px;
  color: #ff4757;
  cursor: pointer;
  white-space: nowrap;
  flex-shrink: 0;
  text-align: center;

  &.disabled {
    color: #999;
    cursor: not-allowed;
  }
}

/* 信息展示项样式 */
.info-item {
  .input-label {
    color: #333;
    font-size: 30px;  // 从34px调小到30px
  }

  .input-value {
    color: #333;
    font-size: 30px;  // 从34px调小到30px
  }
}

/* 按钮区域 */
.button-section {
  padding: 40px 0;
  margin-top: auto;
}

.confirm-button {
  width: 100%;
  height: 96px;
  background: var(--primary-color);
  border-radius: 48px;
  border: none;
  font-size: 32px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    background: var(--primary-dark);
  }

  &::after {
    border: none;
  }
}


