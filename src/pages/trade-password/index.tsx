import { View, Text, Input, Button } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import { useSelector } from 'react-redux'
import { useState } from 'react'
import Taro from '@tarojs/taro'
import type { RootState } from '../../store'
import { userApi } from '../../api/user'
import { authApi } from '../../api/auth'
import './index.scss'

export default function TradePassword() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo)
  const [verifyCode, setVerifyCode] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [countdown, setCountdown] = useState(0)

  useLoad(() => {
    console.log('TradePassword page loaded.')
  })

  // 格式化手机号显示
  const formatPhone = (phone: string) => {
    if (!phone) return ''
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }

  // 获取验证码
  const handleGetCode = async () => {
    if (countdown > 0) return

    if (!(userInfo && userInfo.phone)) {
      Taro.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      })
      return
    }

    try {
      // 调用发送验证码的API
      await authApi.sendSMSCode({ phone: userInfo.phone })

      Taro.showToast({
        title: '验证码已发送',
        icon: 'success'
      })

      // 开始倒计时
      setCountdown(60)
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '发送失败，请重试'
      Taro.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  }

  // 处理输入变化
  const handleVerifyCodeChange = (e: any) => {
    setVerifyCode(e.detail.value)
  }

  const handleNewPasswordChange = (e: any) => {
    setNewPassword(e.detail.value)
  }

  const handleConfirmPasswordChange = (e: any) => {
    setConfirmPassword(e.detail.value)
  }

  // 确认修改
  const handleConfirm = async () => {
    if (!verifyCode.trim()) {
      Taro.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return
    }

    if (!newPassword.trim()) {
      Taro.showToast({
        title: '请输入新交易密码',
        icon: 'none'
      })
      return
    }

    if (newPassword.length !== 6) {
      Taro.showToast({
        title: '交易密码必须为6位数字',
        icon: 'none'
      })
      return
    }

    if (!/^\d{6}$/.test(newPassword)) {
      Taro.showToast({
        title: '交易密码只能为数字',
        icon: 'none'
      })
      return
    }

    if (newPassword !== confirmPassword) {
      Taro.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      })
      return
    }

    try {
      Taro.showLoading({
        title: '设置中...'
      })

      // 调用设置交易密码的API
      await userApi.updateTradePassword({
        code: verifyCode,
        trade_password: newPassword
      })

      Taro.hideLoading()

      Taro.showToast({
        title: '设置成功',
        icon: 'success'
      })

      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)
    } catch (error) {
      Taro.hideLoading()

      const errorMessage = error instanceof Error ? error.message : '设置失败，请重试'
      Taro.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  }

  return (
    <View className='trade-password-page'>
      {/* 输入区域 */}
      <View className='input-section'>
        {/* 绑定手机号 */}
        <View className='input-item info-item'>
          <Text className='input-label'>绑定手机号</Text>
          <Text className='input-value'>{formatPhone((userInfo && userInfo.phone) || '')}</Text>
        </View>

        {/* 验证码输入 */}
        <View className='input-item input-only verify-code-item'>
          <Input
            className='input-field-full'
            value={verifyCode}
            placeholder='请输入验证码'
            placeholderStyle='font-size: 24rpx; color: #999;'
            maxlength={6}
            type='number'
            onInput={handleVerifyCodeChange}
          />
          <Text
            className={`get-code-btn ${countdown > 0 ? 'disabled' : ''}`}
            onClick={handleGetCode}
          >
            {countdown > 0 ? `${countdown}s` : '获取验证码?'}
          </Text>
        </View>

      </View>

      {/* 交易密码输入区域 */}
      <View className='password-section'>
        {/* 新交易密码 */}
        <View className='input-item input-only'>
          <Input
            className='input-field-full'
            value={newPassword}
            placeholder='请输入新交易密码'
            placeholderStyle='font-size: 24rpx; color: #999;'
            maxlength={6}
            type='number'
            password
            onInput={handleNewPasswordChange}
          />
        </View>

        {/* 确认交易密码 */}
        <View className='input-item input-only'>
          <Input
            className='input-field-full'
            value={confirmPassword}
            placeholder='请再次输入新交易密码'
            placeholderStyle='font-size: 24rpx; color: #999;'
            maxlength={6}
            type='number'
            password
            onInput={handleConfirmPasswordChange}
          />
        </View>
      </View>

      {/* 确认按钮 */}
      <View className='button-section'>
        <Button
          className='confirm-button'
          onClick={handleConfirm}
        >
          确认修改
        </Button>
      </View>
    </View>
  )
}
