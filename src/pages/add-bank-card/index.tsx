import { View, Text, Input, Button } from '@tarojs/components'
import { useState } from 'react'
import { useSelector } from 'react-redux'
import Taro, { useLoad } from '@tarojs/taro'
import { accountApi } from '../../api/account'
import { commonApi } from '../../api/common'
import BankSelector from '../../components/BankSelector'
import type { RootState } from '../../store'
import './index.scss'

export default function AddBankCard() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo)
  const [bankAccount, setBankAccount] = useState('')
  const [bankName, setBankName] = useState('')
  const [bankList, setBankList] = useState<string[]>([])
  const [showBankSelector, setShowBankSelector] = useState(false)

  useLoad(() => {
    console.log('AddBankCard page loaded.')
    loadBankList()
  })

  // 加载银行列表
  const loadBankList = async () => {
    try {
      const response = await commonApi.getBanks()
      setBankList(response.banks)
      if (response.banks.length > 0) {
        setBankName(response.banks[0]) // 默认选择第一个银行
      }
    } catch (error) {
      console.error('加载银行列表失败:', error)
      Taro.showToast({
        title: '加载银行列表失败',
        icon: 'none'
      })
    }
  }

  // 显示银行选择器
  const handleShowBankSelector = () => {
    setShowBankSelector(true)
  }

  // 选择银行
  const handleSelectBank = (bank: string) => {
    setBankName(bank)
    setShowBankSelector(false)
  }

  // 处理保存银行卡
  const handleSave = async () => {
    if (!bankAccount.trim()) {
      Taro.showToast({
        title: '请输入银行卡账号',
        icon: 'none'
      })
      return
    }

    if (!bankName.trim()) {
      Taro.showToast({
        title: '请输入开户银行',
        icon: 'none'
      })
      return
    }

    try {
      Taro.showLoading({
        title: '保存中...'
      })

      await accountApi.addBankCard({
        bank_account: bankAccount.trim(),
        bank_name: bankName.trim()
      })

      Taro.hideLoading()

      Taro.showToast({
        title: '保存成功',
        icon: 'success'
      })

      // 延迟返回上一页
      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)

    } catch (error: any) {
      Taro.hideLoading()

      const errorMessage = (error && error.message) || '保存失败，请重试'
      Taro.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  }

  return (
    <View className='add-bank-card-page'>
      {/* 表单区域 */}
      <View className='form-section'>
        {/* 账号类型 */}
        <View className='form-item'>
          <Text className='form-label'>账号类型</Text>
          <Text className='form-value'>银行卡</Text>
        </View>

        {/* 账号姓名 */}
        <View className='form-item'>
          <Text className='form-label'>账号姓名</Text>
          <Text className='form-value'>{(userInfo && userInfo.real_name) || '未设置'}</Text>
        </View>

        {/* 银行账号 */}
        <View className='form-item'>
          <Text className='form-label'>银行账号</Text>
          <Input
            className='form-input'
            placeholder='请输入银行卡账号'
            value={bankAccount}
            onInput={(e) => setBankAccount(e.detail.value)}
          />
        </View>

        {/* 开户银行 */}
        <View className='form-item' onClick={handleShowBankSelector}>
          <Text className='form-label'>开户银行</Text>
          <View className='form-picker'>
            <Text className='picker-text'>
              {bankName || '请选择开户银行'}
            </Text>
            <Text className='picker-arrow'>›</Text>
          </View>
        </View>
      </View>

      {/* 操作提示 */}
      <View className='tips-section'>
        <Text className='tips-title'>操作提示：</Text>
        <Text className='tips-text'>1、请仔细核对银行卡号是否正确。</Text>
        <Text className='tips-text'>2、请填写本人银行卡号，银行卡号和实名信息必须一致，否则无法提现。</Text>
      </View>

      {/* 底部保存按钮 */}
      <View className='bottom-section'>
        <Button
          className='save-button'
          onClick={handleSave}
        >
          保存账号
        </Button>
      </View>

      {/* 银行选择器 */}
      <BankSelector
        visible={showBankSelector}
        bankList={bankList}
        selectedBank={bankName}
        onClose={() => setShowBankSelector(false)}
        onSelect={handleSelectBank}
      />
    </View>
  )
}
