import { View, Text, Input, Button } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import { useSelector, useDispatch } from 'react-redux'
import { useState } from 'react'
import Taro from '@tarojs/taro'
import type { RootState } from '../../store'
import { fetchCurrentUserInfo } from '../../store/user'
import { userApi } from '../../api/user'
import './index.scss'

export default function BasicInfo() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo)
  const dispatch = useDispatch()
  const [nickname, setNickname] = useState((userInfo && userInfo.username) || '')

  useLoad(() => {
    console.log('BasicInfo page loaded.')
  })

  // 处理昵称输入
  const handleNicknameChange = (e: any) => {
    setNickname(e.detail.value)
  }

  // 确认修改
  const handleConfirm = async () => {
    if (!nickname.trim()) {
      Taro.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    if (nickname.length > 20) {
      Taro.showToast({
        title: '昵称不能超过20个字符',
        icon: 'none'
      })
      return
    }

    try {
      // 显示加载提示
      Taro.showLoading({
        title: '修改中...'
      })

      // 调用API更新用户名
      await userApi.updateUsername({ username: nickname })

      // 重新获取用户信息
      await dispatch(fetchCurrentUserInfo())

      Taro.hideLoading()

      Taro.showToast({
        title: '修改成功',
        icon: 'success'
      })

      setTimeout(() => {
        // 跳转回账号设置界面
        Taro.navigateBack()
      }, 1500)
    } catch (error) {
      Taro.hideLoading()

      const errorMessage = error instanceof Error ? error.message : '修改失败，请重试'
      Taro.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  }

  return (
    <View className='basic-info-page'>
      {/* 昵称输入区域 */}
      <View className='input-section'>
        <View className='input-item'>
          <Text className='input-label'>昵称</Text>
          <Input
            className='input-field'
            value={nickname}
            placeholder='请输入昵称'
            maxlength={20}
            onInput={handleNicknameChange}
          />
        </View>
      </View>

      {/* 确认按钮 */}
      <View className='button-section'>
        <Button
          className='confirm-button'
          onClick={handleConfirm}
        >
          确认修改
        </Button>
      </View>
    </View>
  )
}
