.basic-info-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
  display: flex;
  flex-direction: column;
}

/* 输入区域 */
.input-section {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: auto;
}

.input-item {
  display: flex;
  align-items: center;
  padding: 36px 32px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.input-label {
  font-size: 30px;
  color: #333;
  font-weight: 500;
  margin-right: 32px;
  min-width: 120px;
}

.input-field {
  flex: 1;
  font-size: 26px;
  color: #333;
  text-align: right;

  &::placeholder {
    color: #999;
  }
}

/* 按钮区域 */
.button-section {
  padding: 40px 0;
  margin-top: auto;
}

.confirm-button {
  width: 100%;
  height: 96px;
  background: var(--primary-color);
  border-radius: 48px;
  border: none;
  font-size: 32px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    background: var(--primary-dark);
  }

  &::after {
    border: none;
  }
}


