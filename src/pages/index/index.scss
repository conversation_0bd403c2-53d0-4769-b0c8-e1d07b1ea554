.index {
  min-height: 100vh;
  background: white;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
}

/* 顶部区域 */
.header {
  background: white;
  border-radius: 0 0 32px 32px;
}

.title-section {
  text-align: left;
}

.main-title {
  font-size: 36px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8px;
  display: block;
}

.sub-title {
  font-size: 24px;
  color: #666;
  display: block;
}

/* 内容区域 */
.content {
  flex: 1;
}

/* 公告栏 */
.notice-bar {
  background: var(--primary-light);
  border-radius: 16px;
  padding: 8px 12px;
  margin-bottom: 32px;
  display: flex;
  align-items: center;
  overflow: hidden;
  height: 60px;
}

.notice-label {
  font-size: 26px;
  color: var(--primary-color);
  font-weight: 500;
  flex-shrink: 0;
  margin-right: 8px;
}

.notice-content {
  flex: 1;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.notice-scroll-content {
  display: flex;
  flex-direction: column;
  animation: scroll-up 15s linear infinite;
}

.notice-item {
  height: 60px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.notice-text {
  font-size: 26px;
  color: var(--primary-color);
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 上下滚动动画 */
@keyframes scroll-up {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

/* 分类网格 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 40px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.category-icon {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  overflow: hidden;
}

.category-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
}

.icon-text {
  font-size: 32px;
  font-weight: 700;
  color: white;
}

.category-name {
  font-size: 28px;
  color: #333;
  font-weight: 500;
}

/* 服务特色 */
.service-features {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
}

.feature-item {
  flex: 1;
  background: white;
  border-radius: 20px;
  padding: 32px 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: transform 0.2s ease;

  &:active {
    transform: translateY(2px);
  }
}

.feature-icon {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  overflow: hidden;
}

.feature-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.feature-desc {
  font-size: 24px;
  color: #666;
  display: block;
}

/* 公告栏 */
.announcement-bar {
  background: var(--primary-light);
  border-radius: 16px;
  padding: 20px 24px;
  margin-bottom: 32px;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.announcement-icon {
  width: 32px;
  height: 32px;
  margin-right: 16px;
  flex-shrink: 0;
}

.announcement-content {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
}

.announcement-text {
  font-size: 26px;
  color: var(--primary-color);
  font-weight: 500;
  display: inline-block;
  white-space: nowrap;
  animation: scroll-announcement 20s linear infinite;
}

/* 公告滚动动画 */
@keyframes scroll-announcement {
  0% {
    transform: translateX(0);
  }
  20% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 平台介绍卡片 */
.platform-intro {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: 24px;
  padding: 40px 32px;
  margin-bottom: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 8px 32px rgba(18, 150, 219, 0.3);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:active {
    transform: translateY(2px);
    box-shadow: 0 4px 16px rgba(18, 150, 219, 0.3);
  }

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
  }
}

.intro-content {
  flex: 1;
  z-index: 2;
}

.intro-title {
  font-size: 36px;
  font-weight: 700;
  color: white;
  margin-bottom: 12px;
  display: block;
}

.intro-subtitle {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 24px;
  display: block;
}

.intro-button {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 40px;
  padding: 16px 32px;
  display: inline-block;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.button-text {
  font-size: 28px;
  color: white;
  font-weight: 600;
}

.intro-image {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.intro-emoji {
  font-size: 80px;
}

/* 热门回收区域 */
.hot-section {
  margin-bottom: 40px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
}

.section-line {
  width: 6px;
  height: 32px;
  background: var(--primary-color);
  border-radius: 3px;
  margin-right: 12px;
}

.section-title {
  font-size: 32px;
  font-weight: 700;
  color: #333;
}

.section-more {
  font-size: 24px;
  color: #999;
  cursor: pointer;
}

.hot-products {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
}

.hot-product-item {
  width: 190rpx;
  background: white;
  border-radius: 20px;
  padding: 24px 20px;
  text-align: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: transform 0.2s ease;

  &:active {
    transform: translateY(2px);
  }
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  margin: 0 auto 16px;
  display: block;
}

.product-name {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-discount {
  font-size: 20px;
  color: #ff4d4f;
  font-weight: 500;
  background: #fff2f0;
  border-radius: 12px;
  padding: 6px 16px;
  display: inline-block;
}

/* 最近订单 */
.recent-orders {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 32px;
}

.orders-scroll {
  max-height: 300px;
}

.order-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:last-child {
    border-bottom: none;
  }
}

.order-text {
  font-size: 26px;
  color: #333;
  flex: 1;
  margin-right: 16px;
}

.order-time {
  font-size: 22px;
  color: #999;
  flex-shrink: 0;
}

/* 备案号 */
.icp-section {
  text-align: center;
  padding: 40px 20px 20px;
  margin-top: 20px;
}

.icp-text {
  font-size: 24px;
  color: #999;
  font-weight: 400;
}
