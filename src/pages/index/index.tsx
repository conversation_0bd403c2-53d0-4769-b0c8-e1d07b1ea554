import { View, Text, Image } from '@tarojs/components'
import { useLoad, navigateTo, switchTab } from '@tarojs/taro'
import { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import SearchBar from '@/components/SearchBar'
import { productApi } from '@/api/product'
import { commonApi } from '@/api/common'
import type { RootState } from '@/store'
import type { HotProductResponse } from '@/api/types/product'
import type { RecentOrderResponse } from '@/api/types/common'
import './index.scss'

export default function Index() {
  const systemConfig = useSelector((state: RootState) => state.system.config)
  const [hotProducts, setHotProducts] = useState<HotProductResponse[]>([])
  const [recentOrders, setRecentOrders] = useState<RecentOrderResponse[]>([])
  const [loading, setLoading] = useState(true)

  useLoad(() => {
    loadData()
  })

  const loadData = async () => {
    try {
      setLoading(true)
      // 并行加载热门产品和最近订单
      const [hotProductsRes, recentOrdersRes] = await Promise.all([
        productApi.getHotProducts(),
        commonApi.getRecentOrders()
      ])

      setHotProducts(hotProductsRes.list || [])
      setRecentOrders(recentOrdersRes.list || [])
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 跳转到产品详情
  const navigateToProduct = (productId: number) => {
    navigateTo({
      url: `/pages/product-detail/index?id=${productId}`
    })
  }

  // 跳转到分类页面
  const navigateToCategory = (categoryName: string) => {
    navigateTo({
      url: `/pages/card/index?category=${encodeURIComponent(categoryName)}`
    })
  }

  return (
    <View className='index'>
      {/* 顶部标题和搜索 */}
      <View className='header'>
        <View className='title-section'>
          <Text className='main-title'>京海集团旗下卡券回收平台</Text>
          <Text className='sub-title'>安全结算 | 急速变现 | 安全充值 | 口碑保证</Text>
        </View>
        <SearchBar placeholder='请输入您想找、想出售的东西' needPadding={false} />
      </View>

      <View className='content'>
        {/* 公告栏 - 订单提醒 */}
        {recentOrders.length > 0 && (
          <View className='notice-bar'>
            <Text className='notice-label'>订单提醒：</Text>
            <View className='notice-content'>
              <View className='notice-scroll-content'>
                {recentOrders.map((order, index) => (
                  <View key={index} className='notice-item'>
                    <Text className='notice-text'>{order.create_time} {order.msg}</Text>
                  </View>
                ))}
                {/* 重复一遍数据实现无缝滚动 */}
                {recentOrders.map((order, index) => (
                  <View key={`repeat-${index}`} className='notice-item'>
                    <Text className='notice-text'>{order.create_time} {order.msg}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>
        )}

        {/* 分类导航 */}
        {systemConfig && systemConfig.home_recommendations && (
          <View className='category-grid'>
            {systemConfig.home_recommendations.slice(0, 4).map((item, index) => (
              <View
                key={item.id}
                className='category-item'
                onClick={() => {
                  if (item.jump_url) {
                    navigateTo({ url: item.jump_url })
                  }
                }}
              >
                <View className={`category-icon category-${index}`}>
                  <Image
                    className='category-image'
                    src={item.image_url}
                    mode='aspectFit'
                  />
                </View>
                <Text className='category-name'>{item.title}</Text>
              </View>
            ))}
          </View>
        )}

        {/* 服务特色 */}
        {systemConfig && systemConfig.home_recommendations && systemConfig.home_recommendations.length > 4 && (
          <View className='service-features'>
            {systemConfig.home_recommendations.slice(4, 6).map((item, index) => (
              <View
                key={item.id}
                className='feature-item'
                onClick={() => {
                  if (item.jump_url) {
                    switchTab({ url: item.jump_url })
                  }
                }}
              >
                <View className={`feature-icon feature-${index}`}>
                  <Image
                    className='feature-image'
                    src={item.image_url}
                    mode='aspectFit'
                  />
                </View>
                <View className='feature-content'>
                  <Text className='feature-title'>{item.title}</Text>
                  <Text className='feature-desc'>{item.description}</Text>
                </View>
              </View>
            ))}
          </View>
        )}

        {/* 公告 */}
        <View className='announcement-bar'>
          <Image
            className='announcement-icon'
            src={require('@/assets/images/ad_icon.png')}
            mode='aspectFit'
          />
          <View className='announcement-content'>
            <Text className='announcement-text'>
              {(systemConfig && systemConfig.notice) || '正在加载公告信息...'}
            </Text>
          </View>
        </View>

        {/* 平台介绍卡片 */}
        <View
          className='platform-intro'
          onClick={() => {
            switchTab({ url: '/pages/card/index' })
          }}
        >
          <View className='intro-content'>
            <Text className='intro-title'>京海回收自营专属平台</Text>
            <Text className='intro-subtitle'>平台自营 | 安全回收 | 自动结算</Text>
            <View className='intro-button'>
              <Text className='button-text'>立即回收 ▶</Text>
            </View>
          </View>
          <View className='intro-image'>
            <Text className='intro-emoji'>🏆</Text>
          </View>
        </View>

        {/* 热门回收 */}
        <View className='hot-section'>
          <View className='section-header'>
            <View className='section-title-wrapper'>
              <View className='section-line'></View>
              <Text className='section-title'>热门回收</Text>
            </View>
            <Text className='section-more'>查看更多 ></Text>
          </View>
          <View className='hot-products'>
            {hotProducts.map((product) => (
              <View
                key={product.id}
                className='hot-product-item'
                onClick={() => navigateToProduct(product.id)}
              >
                <Image
                  className='product-image'
                  src={product.image_url1}
                  mode='aspectFit'
                />
                <Text className='product-name'>{product.name}</Text>
                <Text className='product-discount'>{product.discount_tip}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* 备案号 */}
        {systemConfig && systemConfig.icp_number && (
          <View className='icp-section'>
            <Text className='icp-text'>{systemConfig.icp_number}</Text>
          </View>
        )}

      </View>
    </View>
  )
}
