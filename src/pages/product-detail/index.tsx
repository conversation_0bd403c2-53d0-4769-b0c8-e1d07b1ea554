import { View, Text, Image, Input, Button, Textarea } from '@tarojs/components'
import { useLoad, useRouter, showToast, navigateBack, switchTab, navigateTo, showModal } from '@tarojs/taro'
import { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { productApi } from '@/api'
import type { ProductDetailResponse, FaceValueResponse, UserReviewResponse } from '@/api/types/product'
import type { RootState } from '@/store'
import './index.scss'

export default function ProductDetail() {
  const router = useRouter()
  const userInfo = useSelector((state: RootState) => state.user.userInfo)
  const [productDetail, setProductDetail] = useState<ProductDetailResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedFaceValue, setSelectedFaceValue] = useState<FaceValueResponse | null>(null)
  const [sellType, setSellType] = useState<'single' | 'batch'>('single')
  const [cardNumber, setCardNumber] = useState('')
  const [cardPassword, setCardPassword] = useState('')
  const [batchCards, setBatchCards] = useState('')
  const [showExchangeInfo, setShowExchangeInfo] = useState(false)
  const [activeTab, setActiveTab] = useState<'exchange' | 'review'>('exchange')
  const [showFaceValueList, setShowFaceValueList] = useState(false)
  const [showFaceValueModal, setShowFaceValueModal] = useState(false)
  const [showCardExampleModal, setShowCardExampleModal] = useState(false)
  const [showAgreementModal, setShowAgreementModal] = useState(false)
  const [agreementChecked, setAgreementChecked] = useState({
    userService: false,
    cardLegal: false,
    faceValueCorrect: false
  })
  const [reviews, setReviews] = useState<UserReviewResponse[]>([])

  // Mock 用户评价数据
  const mockReviews: UserReviewResponse[] = [
    {
      id: 1,
      username: '**想',
      avatar: '',
      content: '这个回收平台很好，处理快，到账快',
      service_rating: 5,
      speed_rating: 5,
      efficiency_rating: 5,
      created_at: '2024-01-30T10:00:00Z',
      time_text: '8小时前'
    },
    {
      id: 2,
      username: '**华',
      avatar: '',
      content: '这个回收平台很好，处理快，到账快',
      service_rating: 5,
      speed_rating: 5,
      efficiency_rating: 5,
      created_at: '2024-01-29T10:00:00Z',
      time_text: '1天前'
    }
  ]

  useLoad(() => {
    const productId = router.params.id
    if (productId) {
      fetchProductDetail(parseInt(productId))
      // 初始化评价数据
      setReviews(mockReviews)
    } else {
      showToast({
        title: '产品ID不存在',
        icon: 'error'
      })
      setTimeout(() => {
        navigateBack()
      }, 1500)
    }
  })

  // 获取产品详情
  const fetchProductDetail = async (id: number) => {
    try {
      setLoading(true)
      const response = await productApi.getProductDetail(id)
      setProductDetail(response)
      // 不默认选择面值，让用户手动选择
    } catch (error) {
      console.error('获取产品详情失败:', error)
      showToast({
        title: '获取产品详情失败',
        icon: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  // 选择面值
  const handleFaceValueSelect = (faceValue: FaceValueResponse) => {
    if (faceValue.status) {
      setSelectedFaceValue(faceValue)
      setShowFaceValueModal(false) // 选择后关闭弹窗
    }
  }

  // 切换出售类型
  const handleSellTypeChange = (type: 'single' | 'batch') => {
    setSellType(type)
  }

  // 立即回收
  const handleRecycle = () => {
    // 检查用户是否已登录
    if (!userInfo || !userInfo.id || userInfo.id <= 0) {
      // 未登录，显示登录确认弹窗
      showModal({
        title: '操作确认',
        content: '兑换卡券需要登录，是否现在登录！',
        cancelText: '取消',
        confirmText: '确定',
        confirmColor: '#1296db',
        success: (res) => {
          if (res.confirm) {
            // 用户点击确定，跳转到登录页面
            navigateTo({
              url: '/pages/login/index'
            })
          }
          // 用户点击取消，不做任何操作
        }
      })
      return
    }

    if (!selectedFaceValue) {
      showToast({
        title: '请选择面值',
        icon: 'none'
      })
      return
    }

    if (sellType === 'single') {
      if (!cardNumber.trim()) {
        showToast({
          title: '请输入卡号',
          icon: 'none'
        })
        return
      }
      if (!cardPassword.trim()) {
        showToast({
          title: '请输入卡密',
          icon: 'none'
        })
        return
      }
    } else {
      if (!batchCards.trim()) {
        showToast({
          title: '请输入批量卡密信息',
          icon: 'none'
        })
        return
      }
    }

    // 所有验证通过，显示协议确认弹窗
    setShowAgreementModal(true)
  }

  // 处理复选框点击
  const handleCheckboxChange = (type: 'userService' | 'cardLegal' | 'faceValueCorrect') => {
    setAgreementChecked(prev => ({
      ...prev,
      [type]: !prev[type]
    }))
  }

  // 检查是否所有协议都已同意
  const isAllAgreementChecked = () => {
    return agreementChecked.userService && agreementChecked.cardLegal && agreementChecked.faceValueCorrect
  }

  // 确认协议并提交回收
  const handleConfirmAgreement = () => {
    if (!isAllAgreementChecked()) {
      showToast({
        title: '请先同意所有协议',
        icon: 'none'
      })
      return
    }

    setShowAgreementModal(false)
    // 重置协议状态
    setAgreementChecked({
      userService: false,
      cardLegal: false,
      faceValueCorrect: false
    })
    // TODO: 实现回收逻辑
    showToast({
      title: '回收功能开发中',
      icon: 'none'
    })
  }

  // 在线客服
  const handleCustomerService = () => {
    showToast({
      title: '客服功能开发中',
      icon: 'none'
    })
  }

  // 切换到卡片页面
  const handleSwitchToCard = () => {
    switchTab({
      url: '/pages/card/index'
    })
  }

  // 渲染星级评分
  const renderStars = (rating: number) => {
    const stars = []
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Image
          key={i}
          className='star'
          src={i <= rating
            ? require('@/assets/images/star_act.png')
            : require('@/assets/images/star_normal.png')
          }
          mode='aspectFit'
        />
      )
    }
    return stars
  }

  // 跳转到帮助页面
  const handleGoToHelp = () => {
    navigateTo({
      url: '/pages/help/index'
    })
  }

  // 跳转到交易步骤页面
  const handleGoToSteps = () => {
    navigateTo({
      url: '/pages/trade-steps/index'
    })
  }

  if (loading) {
    return (
      <View className='product-detail-page'>
        <View className='loading-container'>
          <Text className='loading-text'>加载中...</Text>
        </View>
      </View>
    )
  }

  if (!productDetail) {
    return (
      <View className='product-detail-page'>
        <View className='error-container'>
          <Text className='error-text'>产品不存在</Text>
        </View>
      </View>
    )
  }

  return (
    <View className='product-detail-page'>
      {/* 产品信息区域 */}
      <View className='product-info-section'>
        {/* 产品头部信息 */}
        <View className='product-header'>
          <View className='product-info'>
            <Image
              className='product-icon'
              src={productDetail.image_url1}
              mode='aspectFit'
            />
            <View className='product-text'>
              <Text className='product-name'>{productDetail.name}</Text>
              <Text className='product-category'>归属类型：{productDetail.category.name}</Text>
            </View>
          </View>
          <View className='switch-btn' onClick={handleSwitchToCard}>
            <Text className='switch-text'>切换</Text>
            <View className='switch-arrow'>›</View>
          </View>
        </View>

        {/* 选择面值 */}
        <View className='face-value-section'>
          <View
            className='section-header'
            onClick={() => setShowFaceValueModal(true)}
          >
            <Text className='section-title'>选择面值：</Text>
            <Text className='section-subtitle'>
              {selectedFaceValue ? `${selectedFaceValue.value}.00` : '请选择面值'}
            </Text>
            <View className='section-arrow'>›</View>
          </View>
        </View>

        {/* 回收价格 */}
        <View className='price-section'>
          <Text className='price-label'>回收价格：</Text>
          {selectedFaceValue ? (
            <>
              <Text className='price-value'>
                {(selectedFaceValue.value * parseFloat(selectedFaceValue.my_rate)).toFixed(2)}
              </Text>
              <View className='price-discount'>
                <Text className='price-discount-text'>{(parseFloat(selectedFaceValue.my_rate) * 100).toFixed(2)}折</Text>
              </View>
            </>
          ) : (
            <Text className='price-placeholder'>请先选择面值</Text>
          )}
        </View>
      </View>

      {/* 出售信息区域 */}
      <View className='sell-info-section'>
        {/* 出售类型选择 */}
        <View className='sell-type-section'>
          <View
            className={`sell-type-tab ${sellType === 'single' ? 'active' : ''}`}
            onClick={() => handleSellTypeChange('single')}
          >
            <Text className='sell-type-text'>单卡出售</Text>
            {sellType === 'single' && <View className='active-indicator' />}
          </View>
          <View
            className={`sell-type-tab ${sellType === 'batch' ? 'active' : ''}`}
            onClick={() => handleSellTypeChange('batch')}
          >
            <Text className='sell-type-text'>批量出售</Text>
            {sellType === 'batch' && <View className='active-indicator' />}
          </View>
        </View>

        {/* 根据出售类型显示不同内容 */}
        {sellType === 'single' ? (
          <>
            {/* 卡号输入 */}
            <View className='input-section'>
              <Text className='input-label'>卡号</Text>
              <Input
                className='card-input'
                placeholder='请输入卡号密码'
                value={cardNumber}
                onInput={(e) => setCardNumber(e.detail.value)}
              />
            </View>

            {/* 卡密输入 */}
            <View className='input-section'>
              <Text className='input-label'>卡密</Text>
              <Input
                className='card-input'
                placeholder='请输入卡密密码'
                value={cardPassword}
                onInput={(e) => setCardPassword(e.detail.value)}
              />
            </View>
          </>
        ) : (
          /* 批量出售区域 */
          <View className='batch-info-section'>
            {/* 批量输入文本域 */}
            <View className='batch-input-section'>
              <Textarea
                className='batch-textarea'
                placeholder='卡号与密码之间请使用"空格"隔开，每张卡占用一行用"回车键"隔开'
                placeholderStyle='color: #bbb; font-size: 28rpx;'
                value={batchCards}
                onInput={(e) => setBatchCards(e.detail.value)}
                maxlength={50000}
                autoHeight={false}
                showConfirmBar={false}
              />
            </View>

            <View className='batch-limit'>
              <Text className='batch-limit-text'>每次最多提交1000张</Text>
              <Text
                className='batch-example-link'
                onClick={() => setShowCardExampleModal(true)}
              >
                卡密示例
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* 兑换说明和用户评价 */}
      <View className='exchange-info-section'>
        {/* Tab 头部 */}
        <View className='tab-header'>
          <View
            className={`tab-item ${activeTab === 'exchange' ? 'active' : ''}`}
            onClick={() => setActiveTab('exchange')}
          >
            <Text className='tab-text'>兑换说明</Text>
          </View>
          <View className='tab-divider'></View>
          <View
            className={`tab-item ${activeTab === 'review' ? 'active' : ''}`}
            onClick={() => setActiveTab('review')}
          >
            <Text className='tab-text'>用户评价(55020)</Text>
          </View>
        </View>

        {/* Tab 内容 */}
        <View className='tab-content'>
          {activeTab === 'exchange' ? (
            <View className='exchange-content'>
              <Text className='info-text'>
                官方电话：400-998-7778 在线咨询工作时间：08:30-24:00
              </Text>
              <Text className='info-text'>
                1、声明：京海回收无任何代理，站代理的都属虚假！对所有知悉侵权行为的企业和个人具有法律约束力，若因视本声明等致损失，相关责任将自行承担。
              </Text>
              <Text className='info-text'>
                2、卡号19-21位，卡密16位！无卡号的卡请在卡号栏和卡密栏二空都填卡密！
              </Text>
              <Text className='info-text'>
                3、请仔细核对面值 如实际面值大于提交面值 则按照实际交面
              </Text>
            </View>
          ) : (
            <View className='review-content'>
              {/* 评价头部 */}
              <View className='review-header'>
                <View className='review-title-section'>
                  <Image
                    className='review-icon'
                    src={require('@/assets/images/evaluate_icon.png')}
                    mode='aspectFit'
                  />
                  <Text className='review-title'>用户评价(55021)</Text>
                </View>
                <View className='view-all-section'>
                  <Text className='view-all-text'>查看全部</Text>
                  <Text className='view-all-arrow'>›</Text>
                </View>
              </View>

              {/* 评价列表 */}
              <View className='review-list'>
                {reviews.map((review) => (
                  <View key={review.id} className='review-item'>
                    {/* 用户信息 */}
                    <View className='review-user-info'>
                      <View className='user-left'>
                        <View className='user-avatar'>
                          <View className='avatar-placeholder'>
                            <Text className='avatar-icon'>🐰</Text>
                          </View>
                        </View>
                        <Text className='username'>{review.username}</Text>
                      </View>
                      <Text className='review-time'>{review.time_text}</Text>
                    </View>

                    {/* 评价内容 */}
                    <View className='review-text'>
                      <Text className='review-content-text'>{review.content}</Text>
                    </View>

                    {/* 评分项目 */}
                    <View className='rating-items'>
                      <View className='rating-item'>
                        <Text className='rating-label'>客服服务</Text>
                        <View className='rating-stars'>
                          {renderStars(review.service_rating)}
                        </View>
                      </View>
                      <View className='rating-item'>
                        <Text className='rating-label'>回款速度</Text>
                        <View className='rating-stars'>
                          {renderStars(review.speed_rating)}
                        </View>
                      </View>
                      <View className='rating-item'>
                        <Text className='rating-label'>回收时效</Text>
                        <View className='rating-stars'>
                          {renderStars(review.efficiency_rating)}
                        </View>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            </View>
          )}
        </View>
      </View>

      {/* 交易步骤和常见问题 */}
      <View className='action-section'>
        <View className='action-item' onClick={handleGoToSteps}>
          <Image
            className='action-icon'
            src={require('@/assets/images/buzhou_icon.png')}
            mode='aspectFit'
          />
          <Text className='action-text'>交易步骤</Text>
        </View>
        <View className='action-item' onClick={handleGoToHelp}>
          <Image
            className='action-icon'
            src={require('@/assets/images/wenti_icon.png')}
            mode='aspectFit'
          />
          <Text className='action-text'>常见问题</Text>
        </View>
      </View>

      {/* 底部操作栏 */}
      <View className='bottom-actions'>
        <View className='customer-service' onClick={handleCustomerService}>
          <Image
            className='service-icon'
            src={require('@/assets/images/online_icon.png')}
            mode='aspectFit'
          />
          <Text className='service-text'>在线客服</Text>
        </View>
        <Button className='recycle-btn' onClick={handleRecycle}>
          立即回收
        </Button>
      </View>

      {/* 面值选择弹窗 */}
      {showFaceValueModal && (
        <View className='modal-overlay' onClick={() => setShowFaceValueModal(false)}>
          <View className='face-value-modal' onClick={(e) => e.stopPropagation()}>
            <View className='modal-header'>
              <Text className='modal-title'>选择面值</Text>
              <View className='modal-close' onClick={() => setShowFaceValueModal(false)}>
                <Text className='close-icon'>×</Text>
              </View>
            </View>
            <View className='modal-content'>
              <View className='face-value-grid'>
                {productDetail.face_values.map((faceValue) => (
                  <View
                    key={faceValue.id}
                    className={`face-value-card ${!faceValue.status ? 'disabled' : ''}`}
                    onClick={() => handleFaceValueSelect(faceValue)}
                  >
                    <Text className='face-value-amount'>{faceValue.value}.00</Text>
                    <Text className='face-value-price'>
                      ¥{(faceValue.value * parseFloat(faceValue.my_rate)).toFixed(2)}
                    </Text>
                    <Text className='face-value-discount'>{(parseFloat(faceValue.my_rate) * 100).toFixed(2)}折</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>
        </View>
      )}

      {/* 卡密示例弹窗 */}
      {showCardExampleModal && (
        <View className='example-modal-overlay' onClick={() => setShowCardExampleModal(false)}>
          <View className='card-example-modal' onClick={(e) => e.stopPropagation()}>
            <View className='example-header'>
              <Text className='example-title'>卡密示例</Text>
            </View>
            <View className='example-content'>
              <Text className='example-text'>
                卡号与卡密之间请用英文或则和中文逗号或者空格隔开
              </Text>
              <Text className='example-text'>
                每张卡占用一行用"回车键"隔开
              </Text>
            </View>
            <View className='example-footer'>
              <View
                className='confirm-btn'
                onClick={() => setShowCardExampleModal(false)}
              >
                <Text className='confirm-text'>我已经了解</Text>
              </View>
            </View>
          </View>
        </View>
      )}

      {/* 协议确认弹窗 */}
      {showAgreementModal && (
        <View className='agreement-modal-overlay' onClick={() => setShowAgreementModal(false)}>
          <View className='agreement-modal' onClick={(e) => e.stopPropagation()}>
            <View className='agreement-header'>
              <Text className='agreement-title'>请确认一下协议再提交</Text>
              <View className='agreement-close' onClick={() => setShowAgreementModal(false)}>
                <Text className='close-icon'>×</Text>
              </View>
            </View>
            <View className='agreement-content'>
              <View className='agreement-item'>
                <Image
                  className='agreement-checkbox-img'
                  src={agreementChecked.userService
                    ? require('@/assets/images/chk_act.png')
                    : require('@/assets/images/chk.png')
                  }
                  mode='aspectFit'
                  onClick={() => handleCheckboxChange('userService')}
                />
                <Text className='agreement-text'>
                  我已阅读、理解并接受 <Text
                    className='agreement-link'
                    onClick={() => {
                      navigateTo({
                        url: '/pages/user-agreement/index'
                      })
                    }}
                  >【用户服务协议】</Text>
                </Text>
              </View>
              <View className='agreement-item'>
                <Image
                  className='agreement-checkbox-img'
                  src={agreementChecked.cardLegal
                    ? require('@/assets/images/chk_act.png')
                    : require('@/assets/images/chk.png')
                  }
                  mode='aspectFit'
                  onClick={() => handleCheckboxChange('cardLegal')}
                />
                <Text className='agreement-text'>
                  我已确认该卡号卡密<Text className='agreement-warning'>来源合法，如有问题，本人愿意承担一切法律责任</Text>
                </Text>
              </View>
              <View className='agreement-item'>
                <Image
                  className='agreement-checkbox-img'
                  src={agreementChecked.faceValueCorrect
                    ? require('@/assets/images/chk_act.png')
                    : require('@/assets/images/chk.png')
                  }
                  mode='aspectFit'
                  onClick={() => handleCheckboxChange('faceValueCorrect')}
                />
                <Text className='agreement-text'>
                  我已确认该面值准确无误<Text className='agreement-warning'>如面值错误，余额将无法退还，损失自行承担</Text>
                </Text>
              </View>
            </View>
            <View className='agreement-footer'>
              <Button
                className={`agreement-submit-btn ${!isAllAgreementChecked() ? 'disabled' : ''}`}
                onClick={handleConfirmAgreement}
              >
                立即回收
              </Button>
            </View>
          </View>
        </View>
      )}
    </View>
  )
}
