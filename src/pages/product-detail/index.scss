.product-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 20px 150px 20px;
}

.loading-container,
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-text,
.error-text {
  color: #999;
  font-size: 32px;
}

/* 产品信息区域 */
.product-info-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  margin-bottom: 32px;
  overflow: hidden;
}

/* 产品头部信息 */
.product-header {
  padding: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.product-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.product-icon {
  width: 100px;
  height: 100px;
  border-radius: 12px;
  margin-right: 20px;
  background-color: #f0f0f0;
}

.product-text {
  flex: 1;
}

.product-name {
  font-size: 30px;
  font-weight: bold;
  color: #333;
  margin-bottom: 6px;
  display: block;
}

.product-category {
  font-size: 24px;
  color: #999;
  display: block;
}

.switch-btn {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.switch-btn:active {
  opacity: 0.7;
}

.switch-text {
  font-size: 24px;
  color: #999;
  margin-right: 12px;
}

.switch-arrow {
  font-size: 42px;
  color: #999;
}

/* 选择面值区域 */
.face-value-section {
  padding: 32px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title {
  font-size: 28px;
  color: #333;
  margin-right: 14px;
}

.section-subtitle {
  font-size: 28px;
  color: #333;
  font-weight: bold;
  flex: 1;
}

.section-arrow {
  font-size: 28px;
  color: #999;
}

/* 面值选择弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.face-value-modal {
  width: 100%;
  height: 60vh;
  background: #f8f9fa;
  border-radius: 24px 24px 0 0;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.modal-title {
  font-size: 36px;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-icon {
  font-size: 48px;
  color: #999;
  line-height: 1;
}

.modal-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

.face-value-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.face-value-card {
  background: white;
  border-radius: 16px;
  padding: 32px 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.face-value-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.face-value-amount {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8px;
}

.face-value-price {
  font-size: 24px;
  color: #7f8293;
  font-weight: bold;
  display: block;
  margin-bottom: 4px;
}

.face-value-discount {
  font-size: 20px;
  color: #e60020;
  background: #fcf2f3;
  display: block;
  padding: 8px 12px;
  border-radius: 8px;
  text-align: center;
}

/* 回收价格 */
.price-section {
  padding: 32px;
  display: flex;
  align-items: center;
}

.price-label {
  font-size: 28px;
  color: #333;
  margin-right: 14px;
}

.price-value {
  font-size: 30px;
  color: #ff4d4f;
  font-weight: bold;
}

.price-placeholder {
  font-size: 28px;
  color: #999;
}

.price-discount {
  margin-left: auto;
}

.price-discount-text {
  font-size: 20px;
  color: #e60020;
  background: #fcf2f3;
  padding: 12px 16px;
  border-radius: 8px;
  text-align: center;
}

/* 出售信息区域 */
.sell-info-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  margin-bottom: 32px;
  overflow: hidden;
}

/* 出售类型选择 */
.sell-type-section {
  padding: 0 32px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
}

.sell-type-tab {
  flex: 1;
  padding: 32px 0;
  text-align: center;
  position: relative;
  cursor: pointer;
}

.sell-type-text {
  font-size: 28px;
  color: #666;
}

.sell-type-tab.active .sell-type-text {
  color: var(--primary-color);
  font-weight: bold;
}

.active-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 6px;
  background-color: var(--primary-color);
  border-radius: 3px;
}

/* 输入区域 */
.input-section {
  padding: 32px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.input-section:last-child {
  border-bottom: none;
}

.input-label {
  font-size: 28px;
  color: #333;
  width: 80px;
  margin-right: 32px;
}

.card-input {
  flex: 1;
  font-size: 28px;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.card-input::placeholder {
  color: #ccc;
}

/* 批量出售说明区域 */
.batch-info-section {
  padding: 32px;
}

.batch-input-section {
  margin-bottom: 32px;
}

.batch-textarea {
  width: 100%;
  min-height: 200px;
  padding: 24px;
  font-size: 28px;
  color: #333;
  background: white;
  border: none;
  border-radius: 12px;
  line-height: 1.5;
  resize: none;
  outline: none;
  box-sizing: border-box;
}



.batch-limit {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.batch-limit-text {
  font-size: 28px;
  color: #333;
  font-weight: bold;
}

.batch-example-link {
  font-size: 28px;
  color: var(--primary-color);
  cursor: pointer;
}

/* 兑换说明和用户评价 */
.exchange-info-section {
  background: white;
  margin-bottom: 16px;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

/* Tab 头部 */
.tab-header {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  flex: 1;
  padding: 32px;
  text-align: center;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.tab-text {
  font-size: 28px;
  color: #ccc;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: #000;
  font-weight: bold;
}

.tab-divider {
  width: 1px;
  height: 40px;
  background: #e8e8e8;
  flex-shrink: 0;
}

/* Tab 内容 */
.tab-content {
  padding: 32px;
}

.exchange-content,
.review-content {
  /* 内容区域样式 */
}

.info-text {
  font-size: 24px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
  display: block;
}

.info-text:last-child {
  margin-bottom: 0;
}

/* 用户评价样式 */
.review-content {
  padding: 0;
}

.review-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0 32px 0;
}

.review-title-section {
  display: flex;
  align-items: center;
}

.review-icon {
  width: 40px;
  height: 40px;
  margin-right: 16px;
}

.review-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.view-all-section {
  display: flex;
  align-items: center;
}

.view-all-text {
  font-size: 24px;
  color: #999;
  margin-right: 8px;
}

.view-all-arrow {
  font-size: 28px;
  color: #999;
}

.review-list {
  /* 评价列表容器 */
}

.review-item {
  margin-bottom: 48px;
  padding-bottom: 48px;
  border-bottom: 1px solid #f5f5f5;
}

.review-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.review-user-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.user-left {
  display: flex;
  align-items: center;
}

.user-avatar {
  margin-right: 24px;
}

.avatar-placeholder {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: #ff4757;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 32px;
  color: white;
}

.username {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.review-time {
  font-size: 20px;
  color: #999;
}

.review-text {
  margin-bottom: 32px;
}

.review-content-text {
  font-size: 24px;
  color: #000;
  line-height: 1.6;
}

.rating-items {
  /* 评分项目容器 */
}

.rating-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.rating-item:last-child {
  margin-bottom: 0;
}

.rating-label {
  font-size: 24px;
  color: #333;
  margin-right: 20px;
}

.rating-stars {
  display: flex;
  align-items: center;
}

.star {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.star:last-child {
  margin-right: 0;
}

/* 交易步骤和常见问题区域 */
.action-section {
  display: flex;
  gap: 32px;
  margin-bottom: 32px;
}

.action-item {
  flex: 1;
  background: white;
  border-radius: 16px;
  padding: 32px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.action-icon {
  width: 48px;
  height: 48px;
  margin-right: 16px;
}

.action-text {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  text-align: center;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 32px 32px 40px 32px;
  display: flex;
  align-items: center;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.customer-service {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 32px;
  cursor: pointer;
  min-width: 130px;
  padding: 0 24px;
}

.service-icon {
  width: 64px !important;
  height: 64px !important;
}

.service-text {
  font-size: 28px;
  color: #666;
}

.recycle-btn {
  flex: 1;
  padding: 20px 0;
  background: var(--primary-color);
  color: white;
  font-size: 32px;
  font-weight: bold;
  border-radius: 60px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(18, 150, 219, 0.3);
}

.recycle-btn:active {
  transform: translateY(1px);
  background: var(--primary-hover);
  box-shadow: 0 2px 8px rgba(18, 150, 219, 0.3);
}

/* 卡密示例弹窗 */
.example-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-example-modal {
  width: 600px;
  background: white;
  border-radius: 24px;
  margin: 0 32px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.example-header {
  padding: 48px 48px 32px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.example-title {
  font-size: 36px;
  font-weight: bold;
  color: #333;
}

.example-content {
  padding: 48px;
  text-align: center;
}

.example-text {
  font-size: 28px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 24px;
  display: block;
}

.example-text:last-child {
  margin-bottom: 0;
}

.example-footer {
  padding: 0 48px 48px;
}

.confirm-btn {
  width: 100%;
  height: 88px;
  background: var(--primary-color);
  border-radius: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.confirm-btn:active {
  transform: translateY(1px);
  background: var(--primary-hover);
}

.confirm-text {
  font-size: 28px;
  font-weight: 600;
  color: white;
}

/* 协议确认弹窗 */
.agreement-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
}

.agreement-modal {
  background: white;
  border-radius: 32px 32px 0 0;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.agreement-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40px 32px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.agreement-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
}

.agreement-close {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.agreement-content {
  padding: 32px;
  max-height: 50vh;
  overflow-y: auto;
}

.agreement-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.agreement-checkbox-img {
  width: 32px;
  height: 32px;
  margin-right: 16px;
  margin-top: 4px;
  flex-shrink: 0;
  cursor: pointer;

  &:active {
    opacity: 0.7;
  }
}

.agreement-text {
  font-size: 28px;
  color: #666;
  line-height: 1.6;
  flex: 1;
}

.agreement-link {
  color: #1296db;
  cursor: pointer;
  text-decoration: underline;

  &:active {
    opacity: 0.7;
  }
}

.agreement-warning {
  color: #ff4d4f;
}

.agreement-footer {
  padding: 24px 32px 40px;
  border-top: 1px solid #f0f0f0;
}

.agreement-submit-btn {
  width: 100%;
  padding: 24px 0;
  background: var(--primary-color);
  color: white;
  font-size: 32px;
  font-weight: 600;
  border-radius: 60px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:active {
    background: var(--primary-hover);
  }

  &.disabled {
    background: #ccc;
    color: #999;
    cursor: not-allowed;

    &:active {
      background: #ccc;
      transform: none;
    }
  }
}
